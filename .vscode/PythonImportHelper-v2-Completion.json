[{"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "train_test_split", "importPath": "sklearn.model_selection", "description": "sklearn.model_selection", "isExtraImport": true, "detail": "sklearn.model_selection", "documentation": {}}, {"label": "ast", "kind": 6, "isExtraImport": true, "importPath": "ast", "description": "ast", "detail": "ast", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "TensorDataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "plot_and_save", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "create_results_folder", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "TransformerRegressor", "importPath": "utils.transformer_regressor", "description": "utils.transformer_regressor", "isExtraImport": true, "detail": "utils.transformer_regressor", "documentation": {}}, {"label": "TransformerRegressor", "importPath": "utils.transformer_regressor", "description": "utils.transformer_regressor", "isExtraImport": true, "detail": "utils.transformer_regressor", "documentation": {}}, {"label": "calculate_metrics", "importPath": "utils.metrics", "description": "utils.metrics", "isExtraImport": true, "detail": "utils.metrics", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "torch.nn", "kind": 6, "isExtraImport": true, "importPath": "torch.nn", "description": "torch.nn", "detail": "torch.nn", "documentation": {}}, {"label": "torch.nn.functional", "kind": 6, "isExtraImport": true, "importPath": "torch.nn.functional", "description": "torch.nn.functional", "detail": "torch.nn.functional", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "math", "kind": 6, "isExtraImport": true, "importPath": "math", "description": "math", "detail": "math", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "rearrange", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "repeat", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "random", "kind": 6, "isExtraImport": true, "importPath": "random", "description": "random", "detail": "random", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "seaborn", "kind": 6, "isExtraImport": true, "importPath": "seaborn", "description": "seaborn", "detail": "seaborn", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "AutoTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "AutoModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "psutil", "kind": 6, "isExtraImport": true, "importPath": "psutil", "description": "psutil", "detail": "psutil", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "utils", "kind": 6, "isExtraImport": true, "importPath": "utils", "description": "utils", "detail": "utils", "documentation": {}}, {"label": "visualize_attention_weights", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_cross_attention_flow", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "analyze_attention_patterns", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "print_attention_summary", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "validate_attention_fusion_input", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "seq2attention", "importPath": "seq2attention", "description": "seq2attention", "isExtraImport": true, "detail": "seq2attention", "documentation": {}}, {"label": "batch_seq2attention", "importPath": "seq2attention", "description": "seq2attention", "isExtraImport": true, "detail": "seq2attention", "documentation": {}}, {"label": "get_model_info", "importPath": "seq2attention", "description": "seq2attention", "isExtraImport": true, "detail": "seq2attention", "documentation": {}}, {"label": "seq2attention", "importPath": "seq2attention", "description": "seq2attention", "isExtraImport": true, "detail": "seq2attention", "documentation": {}}, {"label": "batch_seq2attention", "importPath": "seq2attention", "description": "seq2attention", "isExtraImport": true, "detail": "seq2attention", "documentation": {}}, {"label": "get_model_info", "importPath": "seq2attention", "description": "seq2attention", "isExtraImport": true, "detail": "seq2attention", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "esm", "kind": 6, "isExtraImport": true, "importPath": "esm", "description": "esm", "detail": "esm", "documentation": {}}, {"label": "SeqRecord", "importPath": "Bio.SeqRecord", "description": "Bio.SeqRecord", "isExtraImport": true, "detail": "Bio.SeqRecord", "documentation": {}}, {"label": "Seq", "importPath": "Bio.Seq", "description": "Bio.Seq", "isExtraImport": true, "detail": "Bio.Seq", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "pickle", "kind": 6, "isExtraImport": true, "importPath": "pickle", "description": "pickle", "detail": "pickle", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "warnings", "kind": 6, "isExtraImport": true, "importPath": "warnings", "description": "warnings", "detail": "warnings", "documentation": {}}, {"label": "snapshot_download", "importPath": "huggingface_hub", "description": "huggingface_hub", "isExtraImport": true, "detail": "huggingface_hub", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "parse_vector_string", "kind": 2, "importPath": "kcat_part.data_preprocessing", "description": "kcat_part.data_preprocessing", "peekOfCode": "def parse_vector_string(vector_str):\n    \"\"\"解析向量字符串为numpy数组\"\"\"\n    try:\n        # 移除引号并解析为浮点数列表\n        vector_str = vector_str.strip('\"')\n        vector_list = [float(x) for x in vector_str.split(',')]\n        return np.array(vector_list)\n    except Exception as e:\n        print(f\"Error parsing vector: {e}\")\n        return None", "detail": "kcat_part.data_preprocessing", "documentation": {}}, {"label": "load_and_preprocess_data", "kind": 2, "importPath": "kcat_part.data_preprocessing", "description": "kcat_part.data_preprocessing", "peekOfCode": "def load_and_preprocess_data(file_path):\n    \"\"\"加载和预处理数据\"\"\"\n    print(\"Loading data...\")\n    # 读取TSV文件，处理引号问题\n    try:\n        df = pd.read_csv(file_path, sep='\\t', quoting=3, on_bad_lines='skip')\n    except:\n        # 如果上面的方法失败，尝试其他参数\n        try:\n            df = pd.read_csv(file_path, sep='\\t', quotechar='\"', doublequote=True, on_bad_lines='skip')", "detail": "kcat_part.data_preprocessing", "documentation": {}}, {"label": "split_and_save_data", "kind": 2, "importPath": "kcat_part.data_preprocessing", "description": "kcat_part.data_preprocessing", "peekOfCode": "def split_and_save_data(features, labels, test_size=0.2, random_state=42):\n    \"\"\"划分训练测试集并保存\"\"\"\n    print(\"Splitting data...\")\n    X_train, X_test, y_train, y_test = train_test_split(\n        features, labels, test_size=test_size, random_state=random_state\n    )\n    print(f\"Training set: {X_train.shape[0]} samples\")\n    print(f\"Test set: {X_test.shape[0]} samples\")\n    # 创建结果目录\n    os.makedirs('Result', exist_ok=True)", "detail": "kcat_part.data_preprocessing", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "kcat_part.data_preprocessing", "description": "kcat_part.data_preprocessing", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    try:\n        # 加载和预处理数据\n        features, labels = load_and_preprocess_data('kcat_features.tsv')\n        # 划分和保存数据\n        X_train, X_test, y_train, y_test = split_and_save_data(features, labels)\n        print(\"\\nData preprocessing completed successfully!\")\n        print(f\"Training features shape: {X_train.shape}\")\n        print(f\"Training labels shape: {y_train.shape}\")", "detail": "kcat_part.data_preprocessing", "documentation": {}}, {"label": "create_example_data", "kind": 2, "importPath": "kcat_part.example_predict", "description": "kcat_part.example_predict", "peekOfCode": "def create_example_data():\n    \"\"\"创建示例数据\"\"\"\n    print(\"[示例] 创建示例数据...\")\n    # 创建示例CSV文件用于批量预测\n    n_samples = 5\n    smiles_vectors = []\n    sequence_vectors = []\n    for i in range(n_samples):\n        # 生成随机向量 (实际使用中应该是真实的分子和蛋白质向量)\n        smiles_vec = np.random.rand(512)  # SMILES向量维度", "detail": "kcat_part.example_predict", "documentation": {}}, {"label": "demo_single_prediction", "kind": 2, "importPath": "kcat_part.example_predict", "description": "kcat_part.example_predict", "peekOfCode": "def demo_single_prediction():\n    \"\"\"演示单样本预测\"\"\"\n    print(\"\\n\" + \"=\"*50)\n    print(\"[演示] 单样本预测\")\n    print(\"=\"*50)\n    # 创建示例数据\n    smiles_vec, sequence_vec = create_example_data()\n    # 构建命令\n    cmd = f\"\"\"python predict.py --mode single --device cpu \\\n--smiles_vector \"{smiles_vec}\" \\", "detail": "kcat_part.example_predict", "documentation": {}}, {"label": "demo_batch_prediction", "kind": 2, "importPath": "kcat_part.example_predict", "description": "kcat_part.example_predict", "peekOfCode": "def demo_batch_prediction():\n    \"\"\"演示批量预测\"\"\"\n    print(\"\\n\" + \"=\"*50)\n    print(\"[演示] 批量预测\")\n    print(\"=\"*50)\n    # 检查示例文件是否存在\n    if not os.path.exists('example_input.csv'):\n        create_example_data()\n    # 构建命令\n    cmd = \"\"\"python predict.py --mode batch --device cpu \\", "detail": "kcat_part.example_predict", "documentation": {}}, {"label": "show_usage", "kind": 2, "importPath": "kcat_part.example_predict", "description": "kcat_part.example_predict", "peekOfCode": "def show_usage():\n    \"\"\"显示使用说明\"\"\"\n    print(\"[使用说明] predict.py 使用方法\")\n    print(\"=\"*50)\n    usage = \"\"\"\n1. 单样本预测:\n   python predict.py --mode single --device cpu \\\\\n     --smiles_vector \"0.1,0.2,0.3,...\" \\\\\n     --sequence_vector \"0.4,0.5,0.6,...\"\n2. 批量预测:", "detail": "kcat_part.example_predict", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "kcat_part.example_predict", "description": "kcat_part.example_predict", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"[预测示例] Kcat预测脚本使用示例\")\n    print(\"=\"*50)\n    print(\"\\n[菜单] 选择演示:\")\n    print(\"1. 查看使用说明\")\n    print(\"2. 单样本预测演示\")\n    print(\"3. 批量预测演示\")\n    print(\"4. 退出\")\n    while True:", "detail": "kcat_part.example_predict", "documentation": {}}, {"label": "load_best_model", "kind": 2, "importPath": "kcat_part.generate_best_model_plots", "description": "kcat_part.generate_best_model_plots", "peekOfCode": "def load_best_model():\n    \"\"\"加载R²最好的模型\"\"\"\n    print(\"使用R²最好的原始Transformer模型...\")\n    # 直接使用我们知道R²最好的原始模型\n    best_model_path = 'Result/Transformer_Model/20250629_230536/best_model.pt'\n    if not os.path.exists(best_model_path):\n        raise ValueError(f\"最佳模型文件不存在: {best_model_path}\")\n    try:\n        checkpoint = torch.load(best_model_path, map_location='cpu', weights_only=False)\n        print(f\"成功加载模型: {best_model_path}\")", "detail": "kcat_part.generate_best_model_plots", "documentation": {}}, {"label": "create_model_from_checkpoint", "kind": 2, "importPath": "kcat_part.generate_best_model_plots", "description": "kcat_part.generate_best_model_plots", "peekOfCode": "def create_model_from_checkpoint(checkpoint, input_dim):\n    \"\"\"根据checkpoint创建模型\"\"\"\n    # 根据保存的权重推断正确的参数\n    model = TransformerRegressor(\n        input_dim=input_dim,\n        d_model=64,\n        nhead=4,\n        num_layers=2,\n        dim_feedforward=256,  # 从错误信息看应该是256\n        dropout=0.1", "detail": "kcat_part.generate_best_model_plots", "documentation": {}}, {"label": "generate_predictions_and_plots", "kind": 2, "importPath": "kcat_part.generate_best_model_plots", "description": "kcat_part.generate_best_model_plots", "peekOfCode": "def generate_predictions_and_plots():\n    \"\"\"生成预测并绘制散点图\"\"\"\n    print(\"开始生成预测和散点图...\")\n    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    print(f\"使用设备: {device}\")\n    # 1. 加载最佳模型\n    best_model_path, best_model_info, best_r2 = load_best_model()\n    # 2. 加载数据\n    # 首先尝试加载原始数据\n    try:", "detail": "kcat_part.generate_best_model_plots", "documentation": {}}, {"label": "KcatPredictor", "kind": 6, "importPath": "kcat_part.predict", "description": "kcat_part.predict", "peekOfCode": "class KcatPredictor:\n    \"\"\"Kcat预测器\"\"\"\n    def __init__(self, model_path=None, device='auto'):\n        \"\"\"\n        初始化预测器\n        Args:\n            model_path: 模型文件路径，默认使用最佳模型\n            device: 设备选择 ('auto', 'cpu', 'cuda')\n        \"\"\"\n        # 设置设备", "detail": "kcat_part.predict", "documentation": {}}, {"label": "parse_vector_string", "kind": 2, "importPath": "kcat_part.predict", "description": "kcat_part.predict", "peekOfCode": "def parse_vector_string(vector_str):\n    \"\"\"解析向量字符串\"\"\"\n    try:\n        # 移除引号并解析为浮点数列表\n        vector_str = vector_str.strip('\"').strip(\"'\")\n        if vector_str.startswith('[') and vector_str.endswith(']'):\n            # 处理列表格式\n            vector_str = vector_str[1:-1]\n        vector_list = [float(x.strip()) for x in vector_str.split(',')]\n        return np.array(vector_list)", "detail": "kcat_part.predict", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "kcat_part.predict", "description": "kcat_part.predict", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    parser = argparse.ArgumentParser(description='Kcat预测工具')\n    parser.add_argument('--mode', choices=['single', 'batch'], default='single',\n                       help='预测模式: single(单样本) 或 batch(批量)')\n    parser.add_argument('--device', choices=['auto', 'cpu', 'cuda'], default='auto',\n                       help='设备选择: auto(自动), cpu, cuda')\n    parser.add_argument('--model', type=str, default=None,\n                       help='模型文件路径，默认使用最佳模型')\n    # 单样本预测参数", "detail": "kcat_part.predict", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "kcat_part.run_demo", "description": "kcat_part.run_demo", "peekOfCode": "def main():\n    \"\"\"主演示函数\"\"\"\n    print(\"[Kcat] Kcat预测模型演示\")\n    print(\"=\" * 50)\n    print(\"\\n[菜单] 可用操作:\")\n    print(\"1. 查看模型性能和可视化结果\")\n    print(\"2. 重新生成散点图和残差图\")\n    print(\"3. 查看项目结构\")\n    print(\"4. 退出\")\n    while True:", "detail": "kcat_part.run_demo", "documentation": {}}, {"label": "print_project_structure", "kind": 2, "importPath": "kcat_part.run_demo", "description": "kcat_part.run_demo", "peekOfCode": "def print_project_structure():\n    \"\"\"打印项目结构\"\"\"\n    structure = \"\"\"\n[结构] 项目结构:\nkcat/\n├── [数据] kcat_features.tsv                    # 原始数据文件 (393MB)\n├── [工具] data_preprocessing.py                # 数据预处理脚本\n├── [可视化] generate_best_model_plots.py         # 生成散点图脚本\n├── [查看] show_plot_results.py                 # 显示结果脚本\n├── [演示] run_demo.py                          # 演示脚本 (当前)", "detail": "kcat_part.run_demo", "documentation": {}}, {"label": "show_results", "kind": 2, "importPath": "kcat_part.show_plot_results", "description": "kcat_part.show_plot_results", "peekOfCode": "def show_results():\n    \"\"\"显示散点图生成结果\"\"\"\n    results_dir = \"Result/Best_Model_Plots/20250630_105402\"\n    print(\"[结果] R²最佳模型散点图生成结果\")\n    print(\"=\" * 50)\n    # 读取模型信息\n    info_file = os.path.join(results_dir, \"model_info.json\")\n    if os.path.exists(info_file):\n        with open(info_file, 'r') as f:\n            model_info = json.load(f)", "detail": "kcat_part.show_plot_results", "documentation": {}}, {"label": "MultiHeadCrossAttention", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class MultiHeadCrossAttention(nn.Module):\n    \"\"\"多头交叉注意力模块\"\"\"\n    def __init__(self, query_dim: int, key_dim: int, value_dim: int, \n                 hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化多头交叉注意力\n        Args:\n            query_dim: 查询特征维度\n            key_dim: 键特征维度\n            value_dim: 值特征维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "CrossAttentionBlock", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class CrossAttentionBlock(nn.Module):\n    \"\"\"交叉注意力块\"\"\"\n    def __init__(self, esm_dim: int, msa_dim: int, hidden_dim: int, \n                 num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化交叉注意力块\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "MSAFeatureProcessor", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class MSAFeatureProcessor(nn.Module):\n    \"\"\"MSA特征处理器\"\"\"\n    def __init__(self, output_dim: int = 256):\n        \"\"\"\n        初始化MSA特征处理器\n        Args:\n            output_dim: 输出特征维度\n        \"\"\"\n        super().__init__()\n        self.output_dim = output_dim", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class AttentionFusionModel(nn.Module):\n    \"\"\"注意力融合模型主类\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, hidden_dim: int = 512,\n                 num_blocks: int = 4, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化注意力融合模型\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MultiHeadCrossAttention(nn.Module):\n    \"\"\"多头交叉注意力模块\"\"\"\n    def __init__(self, query_dim: int, key_dim: int, value_dim: int, \n                 hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化多头交叉注意力\n        Args:\n            query_dim: 查询特征维度\n            key_dim: 键特征维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "direct_feature_extraction_demo", "kind": 2, "importPath": "protein2vector.attention_fusion.direct_usage_demo", "description": "protein2vector.attention_fusion.direct_usage_demo", "peekOfCode": "def direct_feature_extraction_demo():\n    \"\"\"演示直接特征提取\"\"\"\n    print(\"Attention Fusion 直接使用演示\")\n    print(\"=\" * 50)\n    # 初始化模型（无需训练）\n    print(\"1. 初始化模型...\")\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    extractor = ProteinFeatureExtractor()\n    fusion_model = AttentionFusionModel(\n        esm_dim=1280, ", "detail": "protein2vector.attention_fusion.direct_usage_demo", "documentation": {}}, {"label": "compare_features", "kind": 2, "importPath": "protein2vector.attention_fusion.direct_usage_demo", "description": "protein2vector.attention_fusion.direct_usage_demo", "peekOfCode": "def compare_features():\n    \"\"\"比较不同特征的效果\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(\"特征比较分析\")\n    print(\"=\" * 50)\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    extractor = ProteinFeatureExtractor()\n    fusion_model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)\n    fusion_model.to(device)\n    # 测试序列", "detail": "protein2vector.attention_fusion.direct_usage_demo", "documentation": {}}, {"label": "usage_recommendations", "kind": 2, "importPath": "protein2vector.attention_fusion.direct_usage_demo", "description": "protein2vector.attention_fusion.direct_usage_demo", "peekOfCode": "def usage_recommendations():\n    \"\"\"使用建议\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(\"使用建议\")\n    print(\"=\" * 50)\n    print(\"\"\"\n   直接使用场景:\n    蛋白质特征提取\n    序列表示学习\n    蛋白质相似性分析", "detail": "protein2vector.attention_fusion.direct_usage_demo", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.direct_usage_demo", "description": "protein2vector.attention_fusion.direct_usage_demo", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 直接使用演示\n    direct_feature_extraction_demo()\n    # 特征比较\n    compare_features()\n    # 使用建议\n    usage_recommendations()\n    print(\"\\n\" + \"=\" * 50)\n    print(\"演示完成!\")", "detail": "protein2vector.attention_fusion.direct_usage_demo", "documentation": {}}, {"label": "download_models", "kind": 2, "importPath": "protein2vector.attention_fusion.download_models", "description": "protein2vector.attention_fusion.download_models", "peekOfCode": "def download_models():\n    \"\"\"下载所需模型\"\"\"\n    try:\n        from huggingface_hub import snapshot_download\n    except ImportError:\n        print(\"请先安装huggingface-hub: pip install huggingface-hub\")\n        return False\n    # 模型列表\n    models = [\n        \"facebook/esm2_t33_650M_UR50D\",", "detail": "protein2vector.attention_fusion.download_models", "documentation": {}}, {"label": "download_facebook_msa", "kind": 2, "importPath": "protein2vector.attention_fusion.download_msa_only", "description": "protein2vector.attention_fusion.download_msa_only", "peekOfCode": "def download_facebook_msa():\n    \"\"\"下载Facebook官方MSA模型\"\"\"\n    print(\"   下载Facebook官方MSA模型...\")\n    print(\"模型: facebook/esm_msa1b_t12_100M_UR50S\")\n    try:\n        import esm\n        print(\"使用fair-esm库下载模型...\")\n        # 这会自动从Facebook官方源下载模型\n        print(\"正在下载MSA Transformer模型...\")\n        model, alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()", "detail": "protein2vector.attention_fusion.download_msa_only", "documentation": {}}, {"label": "test_msa_integration", "kind": 2, "importPath": "protein2vector.attention_fusion.download_msa_only", "description": "protein2vector.attention_fusion.download_msa_only", "peekOfCode": "def test_msa_integration():\n    \"\"\"测试MSA模型与项目的集成\"\"\"\n    print(\"\\n🧪 测试MSA模型集成...\")\n    try:\n        from feature_extractor import ProteinFeatureExtractor\n        print(\"初始化特征提取器...\")\n        extractor = ProteinFeatureExtractor()\n        # 检查MSA模型是否正确加载\n        if hasattr(extractor, 'msa_extractor') and extractor.msa_extractor.msa_model is not None:\n            if hasattr(extractor.msa_extractor, 'use_fair_esm_msa') and extractor.msa_extractor.use_fair_esm_msa:", "detail": "protein2vector.attention_fusion.download_msa_only", "documentation": {}}, {"label": "show_download_status", "kind": 2, "importPath": "protein2vector.attention_fusion.download_msa_only", "description": "protein2vector.attention_fusion.download_msa_only", "peekOfCode": "def show_download_status():\n    \"\"\"显示下载状态\"\"\"\n    print(\"\\n   下载状态检查:\")\n    print(\"=\" * 40)\n    # 检查torch hub缓存\n    torch_cache = Path.home() / \".cache\" / \"torch\" / \"hub\" / \"checkpoints\"\n    msa_files = [\n        \"esm_msa1b_t12_100M_UR50S.pt\",\n        \"esm_msa1b_t12_100M_UR50S-contact-regression.pt\"\n    ]", "detail": "protein2vector.attention_fusion.download_msa_only", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.download_msa_only", "description": "protein2vector.attention_fusion.download_msa_only", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"Facebook MSA模型下载工具\")\n    print(\"=\" * 40)\n    # 显示当前状态\n    show_download_status()\n    # 下载模型\n    print(\"\\n开始下载Facebook官方MSA模型...\")\n    if download_facebook_msa():\n        print(\"\\n   MSA模型下载完成!\")", "detail": "protein2vector.attention_fusion.download_msa_only", "documentation": {}}, {"label": "generate_protein_sequence", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "def generate_protein_sequence(length: int = 100) -> str:\n    \"\"\"\n    生成指定长度的随机蛋白质序列\n    Args:\n        length: 序列长度\n    Returns:\n        蛋白质序列字符串\n    \"\"\"\n    # 20种标准氨基酸，按自然频率加权\n    amino_acids = \"ACDEFGHIKLMNPQRSTVWY\"", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "load_esm2_model", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "def load_esm2_model(model_name: str = \"esm2_t33_650M_UR50D\") -> Tuple[object, object]:\n    \"\"\"\n    加载ESM-2模型和tokenizer\n    Args:\n        model_name: 模型名称\n    Returns:\n        (model, tokenizer)\n    \"\"\"\n    try:\n        import esm", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "extract_esm2_features", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "def extract_esm2_features(sequence: str, model, batch_converter, device) -> Dict[str, torch.Tensor]:\n    \"\"\"\n    使用ESM-2提取蛋白质序列特征\n    Args:\n        sequence: 蛋白质序列\n        model: ESM-2模型\n        batch_converter: 批处理转换器\n        device: 计算设备\n    Returns:\n        特征字典", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "analyze_feature_matrix", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "def analyze_feature_matrix(features: Dict[str, torch.Tensor], sequence: str) -> Dict[str, any]:\n    \"\"\"\n    分析ESM-2特征矩阵\n    Args:\n        features: ESM-2特征字典\n        sequence: 原始序列\n    Returns:\n        分析结果\n    \"\"\"\n    logger.info(\"Analyzing ESM-2 feature matrix...\")", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "visualize_feature_matrix", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "def visualize_feature_matrix(analysis: Dict[str, any], output_dir: Path):\n    \"\"\"\n    可视化ESM-2特征矩阵\n    Args:\n        analysis: 分析结果\n        output_dir: 输出目录\n    \"\"\"\n    logger.info(\"Creating visualizations...\")\n    feature_matrix = analysis[\"feature_matrix\"]\n    sequence = analysis[\"sequence\"]", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "save_results", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "def save_results(analysis: Dict[str, any], features: Dict[str, torch.Tensor], output_dir: Path):\n    \"\"\"\n    保存分析结果\n    Args:\n        analysis: 分析结果\n        features: ESM-2特征\n        output_dir: 输出目录\n    \"\"\"\n    logger.info(\"Saving results...\")\n    # 保存特征矩阵", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"🧬 ESM-2 蛋白质序列特征提取演示\")\n    print(\"=\" * 50)\n    # 设置随机种子\n    random.seed(42)\n    np.random.seed(42)\n    torch.manual_seed(42)\n    # 1. 生成100长度的蛋白质序列\n    print(\"\\n1. 生成蛋白质序列...\")", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.esm2_demo", "description": "protein2vector.attention_fusion.esm2_demo", "peekOfCode": "logger = logging.getLogger(__name__)\ndef generate_protein_sequence(length: int = 100) -> str:\n    \"\"\"\n    生成指定长度的随机蛋白质序列\n    Args:\n        length: 序列长度\n    Returns:\n        蛋白质序列字符串\n    \"\"\"\n    # 20种标准氨基酸，按自然频率加权", "detail": "protein2vector.attention_fusion.esm2_demo", "documentation": {}}, {"label": "analyze_esm2_length_limits", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_length_analysis", "description": "protein2vector.attention_fusion.esm2_length_analysis", "peekOfCode": "def analyze_esm2_length_limits():\n    \"\"\"分析ESM-2的长度限制\"\"\"\n    print(\"🧬 ESM-2序列长度分析\")\n    print(\"=\" * 50)\n    # 检查设备和内存\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    print(f\"设备: {device}\")\n    if torch.cuda.is_available():\n        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3\n        print(f\"GPU内存: {gpu_memory:.1f} GB\")", "detail": "protein2vector.attention_fusion.esm2_length_analysis", "documentation": {}}, {"label": "test_actual_memory_usage", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_length_analysis", "description": "protein2vector.attention_fusion.esm2_length_analysis", "peekOfCode": "def test_actual_memory_usage():\n    \"\"\"测试实际内存使用情况\"\"\"\n    print(f\"\\n🧪 实际内存测试:\")\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    if not torch.cuda.is_available():\n        print(\"  ⚠️ 需要GPU进行内存测试\")\n        return\n    try:\n        # 加载模型\n        print(\"  加载ESM-2模型...\")", "detail": "protein2vector.attention_fusion.esm2_length_analysis", "documentation": {}}, {"label": "generate_length_recommendations", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_length_analysis", "description": "protein2vector.attention_fusion.esm2_length_analysis", "peekOfCode": "def generate_length_recommendations():\n    \"\"\"生成长度设置建议\"\"\"\n    print(f\"\\n💡 长度设置建议:\")\n    scenarios = {\n        \"高精度分析\": {\n            \"max_length\": 1000,\n            \"strategy\": \"截断到1000\",\n            \"pros\": [\"最高精度\", \"稳定性好\", \"内存安全\"],\n            \"cons\": [\"可能丢失末端信息\"]\n        },", "detail": "protein2vector.attention_fusion.esm2_length_analysis", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.esm2_length_analysis", "description": "protein2vector.attention_fusion.esm2_length_analysis", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 分析长度限制\n    stats = analyze_esm2_length_limits()\n    # 测试实际内存使用\n    test_actual_memory_usage()\n    # 生成建议\n    generate_length_recommendations()\n    print(f\"\\n\" + \"=\" * 50)\n    print(f\"🎯 结论:\")", "detail": "protein2vector.attention_fusion.esm2_length_analysis", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.esm2_length_analysis", "description": "protein2vector.attention_fusion.esm2_length_analysis", "peekOfCode": "logger = logging.getLogger(__name__)\ndef analyze_esm2_length_limits():\n    \"\"\"分析ESM-2的长度限制\"\"\"\n    print(\"🧬 ESM-2序列长度分析\")\n    print(\"=\" * 50)\n    # 检查设备和内存\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    print(f\"设备: {device}\")\n    if torch.cuda.is_available():\n        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3", "detail": "protein2vector.attention_fusion.esm2_length_analysis", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.example", "description": "protein2vector.attention_fusion.example", "peekOfCode": "def main():\n    \"\"\"主函数，演示注意力融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"注意力融合蛋白质特征提取模型示例\")\n    print(\"=\" * 80)", "detail": "protein2vector.attention_fusion.example", "documentation": {}}, {"label": "demonstrate_attention_mechanisms", "kind": 2, "importPath": "protein2vector.attention_fusion.example", "description": "protein2vector.attention_fusion.example", "peekOfCode": "def demonstrate_attention_mechanisms(model):\n    \"\"\"演示注意力机制的工作原理\"\"\"\n    print(\"\\n1. 注意力机制测试...\")\n    # 创建测试数据\n    batch_size, seq_len = 1, 50\n    esm_dim, msa_dim = 1280, 256\n    # 模拟输入数据\n    esm_features = torch.randn(batch_size, seq_len, esm_dim)\n    msa_features = {\n        \"query_representation\": torch.randn(seq_len, msa_dim),", "detail": "protein2vector.attention_fusion.example", "documentation": {}}, {"label": "compare_fusion_methods", "kind": 2, "importPath": "protein2vector.attention_fusion.example", "description": "protein2vector.attention_fusion.example", "peekOfCode": "def compare_fusion_methods():\n    \"\"\"比较不同融合方法的效果\"\"\"\n    print(\"\\n\" + \"=\"*60)\n    print(\"融合方法比较\")\n    print(\"=\"*60)\n    # 这里可以添加与门控融合和架构级融合的比较代码\n    # 由于篇幅限制，这里只是一个框架\n    print(\"注意力融合的优势:\")\n    print(\"  ✓ 能够捕获复杂的特征间关系\")\n    print(\"  ✓ 提供可解释的注意力权重\")", "detail": "protein2vector.attention_fusion.example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.example", "description": "protein2vector.attention_fusion.example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"主函数，演示注意力融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"注意力融合蛋白质特征提取模型示例\")", "detail": "protein2vector.attention_fusion.example", "documentation": {}}, {"label": "example_single_sequence", "kind": 2, "importPath": "protein2vector.attention_fusion.example_usage", "description": "protein2vector.attention_fusion.example_usage", "peekOfCode": "def example_single_sequence():\n    \"\"\"示例1: 处理单个蛋白质序列\"\"\"\n    print(\"=\" * 60)\n    print(\"示例1: 处理单个蛋白质序列\")\n    print(\"=\" * 60)\n    # 示例蛋白质序列\n    sequence = \"MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD\"\n    print(f\"输入序列: {sequence[:50]}...（长度: {len(sequence)}）\")\n    # 提取特征向量\n    vector = seq2attention(sequence, verbose=True)", "detail": "protein2vector.attention_fusion.example_usage", "documentation": {}}, {"label": "example_multiple_sequences", "kind": 2, "importPath": "protein2vector.attention_fusion.example_usage", "description": "protein2vector.attention_fusion.example_usage", "peekOfCode": "def example_multiple_sequences():\n    \"\"\"示例2: 批量处理多个蛋白质序列\"\"\"\n    print(\"\\n\" + \"=\" * 60)\n    print(\"示例2: 批量处理多个蛋白质序列\")\n    print(\"=\" * 60)\n    # 多个示例序列\n    sequences = [\n        \"ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY\",  # 短序列\n        \"MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD\",  # 长序列\n        \"MKLLVLSLCFLAVFTVFMSISSHAKGFKQVDQAIDQINQKWGKGVTQIKVVQFQKGQKYVVYRVDTQAYHAHTATQKTVDGPSGKLWRDGRGAAQNIIPASTGAAKAVGKVIPELNGKLTGMAFRVPTPNVSVVDLTCRLEKPAKYDDIKKVVKQASEGPLKGILGYTEHQVVSSDFNSDTHSSTFDAGAGIALNDHFVKLISWYDNEFGYSNRVVDLMAHMASKE\",  # 中等长度序列", "detail": "protein2vector.attention_fusion.example_usage", "documentation": {}}, {"label": "example_dataframe_processing", "kind": 2, "importPath": "protein2vector.attention_fusion.example_usage", "description": "protein2vector.attention_fusion.example_usage", "peekOfCode": "def example_dataframe_processing():\n    \"\"\"示例3: 处理DataFrame中的序列数据\"\"\"\n    print(\"\\n\" + \"=\" * 60)\n    print(\"示例3: 处理DataFrame中的序列数据\")\n    print(\"=\" * 60)\n    # 创建示例DataFrame\n    data = {\n        'protein_id': ['P001', 'P002', 'P003'],\n        'protein_name': ['Protein A', 'Protein B', 'Protein C'],\n        'sequence': [", "detail": "protein2vector.attention_fusion.example_usage", "documentation": {}}, {"label": "example_similarity_analysis", "kind": 2, "importPath": "protein2vector.attention_fusion.example_usage", "description": "protein2vector.attention_fusion.example_usage", "peekOfCode": "def example_similarity_analysis():\n    \"\"\"示例4: 使用特征向量进行相似性分析\"\"\"\n    print(\"\\n\" + \"=\" * 60)\n    print(\"示例4: 使用特征向量进行相似性分析\")\n    print(\"=\" * 60)\n    # 两个相似的序列（一个是另一个的突变体）\n    wild_type = \"MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD\"\n    mutant = \"MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPAKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD\"\n    print(\"比较野生型和突变体蛋白质:\")\n    print(f\"野生型: {wild_type[:50]}...\")", "detail": "protein2vector.attention_fusion.example_usage", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.example_usage", "description": "protein2vector.attention_fusion.example_usage", "peekOfCode": "def main():\n    \"\"\"主函数：运行所有示例\"\"\"\n    print(\"seq2attention函数使用示例\")\n    print(\"=\" * 60)\n    # 显示模型信息\n    model_info = get_model_info()\n    print(\"模型配置信息:\")\n    for key, value in model_info.items():\n        print(f\"  {key}: {value}\")\n    try:", "detail": "protein2vector.attention_fusion.example_usage", "documentation": {}}, {"label": "MSATransformerFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor", "description": "protein2vector.attention_fusion.feature_extractor", "peekOfCode": "class MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器 - 修复版本\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        self.model_name = model_name\n        # 尝试加载MSA模型\n        try:\n            logger.info(f\"Loading MSA Transformer model: {model_name}\")\n            # 首先尝试使用fair-esm库\n            try:", "detail": "protein2vector.attention_fusion.feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor", "description": "protein2vector.attention_fusion.feature_extractor", "peekOfCode": "class ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器 - 修复版本\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\",\n                 msa_model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n            msa_model_name: MSA Transformer模型名称\n        \"\"\"", "detail": "protein2vector.attention_fusion.feature_extractor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.feature_extractor", "description": "protein2vector.attention_fusion.feature_extractor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器 - 修复版本\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        self.model_name = model_name\n        # 尝试加载MSA模型\n        try:\n            logger.info(f\"Loading MSA Transformer model: {model_name}\")\n            # 首先尝试使用fair-esm库", "detail": "protein2vector.attention_fusion.feature_extractor", "documentation": {}}, {"label": "MSATransformerFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor_backup", "description": "protein2vector.attention_fusion.feature_extractor_backup", "peekOfCode": "class MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化MSA Transformer特征提取器\n        Args:\n            model_name: MSA Transformer模型名称\n        \"\"\"\n        self.model_name = model_name\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")", "detail": "protein2vector.attention_fusion.feature_extractor_backup", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor_backup", "description": "protein2vector.attention_fusion.feature_extractor_backup", "peekOfCode": "class ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器，支持ESM-2和MSA特征提取\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\",\n                 msa_model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n            msa_model_name: MSA Transformer模型名称\n        \"\"\"", "detail": "protein2vector.attention_fusion.feature_extractor_backup", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.feature_extractor_backup", "description": "protein2vector.attention_fusion.feature_extractor_backup", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化MSA Transformer特征提取器\n        Args:\n            model_name: MSA Transformer模型名称\n        \"\"\"\n        self.model_name = model_name", "detail": "protein2vector.attention_fusion.feature_extractor_backup", "documentation": {}}, {"label": "MSATransformerFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor_fixed", "description": "protein2vector.attention_fusion.feature_extractor_fixed", "peekOfCode": "class MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器 - 修复版本\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        self.model_name = model_name\n        # 尝试加载MSA模型\n        try:\n            logger.info(f\"Loading MSA Transformer model: {model_name}\")\n            # 首先尝试使用fair-esm库\n            try:", "detail": "protein2vector.attention_fusion.feature_extractor_fixed", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor_fixed", "description": "protein2vector.attention_fusion.feature_extractor_fixed", "peekOfCode": "class ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器 - 修复版本\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\",\n                 msa_model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n            msa_model_name: MSA Transformer模型名称\n        \"\"\"", "detail": "protein2vector.attention_fusion.feature_extractor_fixed", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.feature_extractor_fixed", "description": "protein2vector.attention_fusion.feature_extractor_fixed", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器 - 修复版本\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        self.model_name = model_name\n        # 尝试加载MSA模型\n        try:\n            logger.info(f\"Loading MSA Transformer model: {model_name}\")\n            # 首先尝试使用fair-esm库", "detail": "protein2vector.attention_fusion.feature_extractor_fixed", "documentation": {}}, {"label": "demonstrate_fusion_principle", "kind": 2, "importPath": "protein2vector.attention_fusion.fusion_principle_demo", "description": "protein2vector.attention_fusion.fusion_principle_demo", "peekOfCode": "def demonstrate_fusion_principle():\n    \"\"\"演示特征融合的核心原理\"\"\"\n    print(\"🧬 特征融合原理演示\")\n    print(\"=\" * 50)\n    # 1. 模拟原始特征\n    seq_len = 10  # 简化序列长度便于演示\n    esm_dim = 1280\n    msa_dim = 768\n    hidden_dim = 512\n    print(f\"1. 原始特征维度:\")", "detail": "protein2vector.attention_fusion.fusion_principle_demo", "documentation": {}}, {"label": "analyze_attention_patterns", "kind": 2, "importPath": "protein2vector.attention_fusion.fusion_principle_demo", "description": "protein2vector.attention_fusion.fusion_principle_demo", "peekOfCode": "def analyze_attention_patterns(results):\n    \"\"\"分析注意力模式\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(\"🔍 注意力模式分析\")\n    print(\"=\" * 50)\n    attention_esm_to_msa = results['attention_esm_to_msa']\n    attention_msa_to_esm = results['attention_msa_to_esm']\n    # 1. 注意力集中度分析\n    print(\"1. 注意力集中度分析:\")\n    # 每个位置的最大注意力权重", "detail": "protein2vector.attention_fusion.fusion_principle_demo", "documentation": {}}, {"label": "visualize_fusion_process", "kind": 2, "importPath": "protein2vector.attention_fusion.fusion_principle_demo", "description": "protein2vector.attention_fusion.fusion_principle_demo", "peekOfCode": "def visualize_fusion_process(results):\n    \"\"\"可视化融合过程\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(\"🎨 融合过程可视化\")\n    print(\"=\" * 50)\n    # 创建输出目录\n    output_dir = Path(\"fusion_demo_output\")\n    output_dir.mkdir(exist_ok=True)\n    # 设置matplotlib后端\n    import matplotlib", "detail": "protein2vector.attention_fusion.fusion_principle_demo", "documentation": {}}, {"label": "demonstrate_why_no_training", "kind": 2, "importPath": "protein2vector.attention_fusion.fusion_principle_demo", "description": "protein2vector.attention_fusion.fusion_principle_demo", "peekOfCode": "def demonstrate_why_no_training():\n    \"\"\"演示为什么不需要训练\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(\"[IDEA] 为什么不需要训练？\")\n    print(\"=\" * 50)\n    print(\"\"\"\n关键原理解释:\n1. [BRAIN] 预训练模型已经学会了什么？\n   ├── ESM-2: 在6.5亿蛋白质序列上学习了氨基酸语言模式\n   ├── MSA Transformer: 在大规模MSA数据上学习了进化保守性", "detail": "protein2vector.attention_fusion.fusion_principle_demo", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.fusion_principle_demo", "description": "protein2vector.attention_fusion.fusion_principle_demo", "peekOfCode": "def main():\n    \"\"\"主演示函数\"\"\"\n    print(\"[SCIENCE] Attention Fusion 技术原理深度演示\")\n    print(\"=\" * 60)\n    # 1. 演示融合原理\n    results = demonstrate_fusion_principle()\n    # 2. 分析注意力模式\n    analyze_attention_patterns(results)\n    # 3. 可视化融合过程\n    visualize_fusion_process(results)", "detail": "protein2vector.attention_fusion.fusion_principle_demo", "documentation": {}}, {"label": "monitor_progress", "kind": 2, "importPath": "protein2vector.attention_fusion.monitor_kcat_progress", "description": "protein2vector.attention_fusion.monitor_kcat_progress", "peekOfCode": "def monitor_progress():\n    \"\"\"监控处理进度\"\"\"\n    output_file = \"kcatData_20250625.csv\"\n    total_sequences = 18265\n    print(\"KcatData处理进度监控\")\n    print(\"=\" * 50)\n    print(f\"目标文件: {output_file}\")\n    print(f\"总序列数: {total_sequences:,}\")\n    print(\"=\" * 50)\n    try:", "detail": "protein2vector.attention_fusion.monitor_kcat_progress", "documentation": {}}, {"label": "check_current_status", "kind": 2, "importPath": "protein2vector.attention_fusion.monitor_kcat_progress", "description": "protein2vector.attention_fusion.monitor_kcat_progress", "peekOfCode": "def check_current_status():\n    \"\"\"检查当前状态\"\"\"\n    output_file = \"kcatData_20250625.csv\"\n    total_sequences = 18265\n    print(\"当前处理状态\")\n    print(\"=\" * 40)\n    # 检查文件\n    if os.path.exists(output_file):\n        # 读取文件行数\n        with open(output_file, 'r') as f:", "detail": "protein2vector.attention_fusion.monitor_kcat_progress", "documentation": {}}, {"label": "monitor_progress", "kind": 2, "importPath": "protein2vector.attention_fusion.monitor_progress", "description": "protein2vector.attention_fusion.monitor_progress", "peekOfCode": "def monitor_progress():\n    \"\"\"监控处理进度\"\"\"\n    output_file = \"../20250623_Data/Km_Data_fusion_vector.csv\"\n    progress_file = \"../20250623_Data/processing_progress.json\"\n    total_sequences = 26626\n    print(\"🔍 Km_Data处理进度监控\")\n    print(\"=\" * 50)\n    while True:\n        try:\n            # 检查输出文件", "detail": "protein2vector.attention_fusion.monitor_progress", "documentation": {}}, {"label": "show_current_status", "kind": 2, "importPath": "protein2vector.attention_fusion.monitor_progress", "description": "protein2vector.attention_fusion.monitor_progress", "peekOfCode": "def show_current_status():\n    \"\"\"显示当前状态\"\"\"\n    output_file = \"../20250623_Data/Km_Data_fusion_vector.csv\"\n    progress_file = \"../20250623_Data/processing_progress.json\"\n    total_sequences = 26626\n    print(\"📊 当前处理状态\")\n    print(\"=\" * 30)\n    # 检查文件\n    if os.path.exists(output_file):\n        # 读取文件行数", "detail": "protein2vector.attention_fusion.monitor_progress", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.monitor_progress", "description": "protein2vector.attention_fusion.monitor_progress", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    import sys\n    if len(sys.argv) > 1 and sys.argv[1] == \"status\":\n        show_current_status()\n    else:\n        monitor_progress()\nif __name__ == \"__main__\":\n    main()", "detail": "protein2vector.attention_fusion.monitor_progress", "documentation": {}}, {"label": "analyze_msa_feature_extraction_error", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def analyze_msa_feature_extraction_error():\n    \"\"\"分析MSA特征提取错误的具体影响\"\"\"\n    print(\"=\" * 80)\n    print(\"MSA特征提取错误对21维特征的影响分析\")\n    print(\"=\" * 80)\n    print(\"🔍 错误发生的具体位置:\")\n    print(\"   1. MSA Transformer模型加载成功\")\n    print(\"   2. 在extract_msa_transformer_features()方法中\")\n    print(\"   3. tokenizer处理输入时出现: 'list' object has no attribute 'strip'\")\n    print(\"   4. 触发except块，调用_extract_traditional_msa_features()\")", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "demonstrate_traditional_feature_extraction", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def demonstrate_traditional_feature_extraction():\n    \"\"\"演示传统MSA特征提取过程\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"传统MSA特征提取过程详细演示\")\n    print(\"=\" * 80)\n    # 模拟一个蛋白质序列\n    sequence = \"MKLLVLGLGAGVGKSALTIQLIQNHFVDEYDPTIEDSYRKQVVIDGETCLLDILDTAGQEEYSAMRDQYMRTGEGFLCVFAINNTKSFEDIHQYREQIKRVKDSDDVPMVLVGNKCDLAARTVESRQAQDLARSYGIPYIETSAKTRQGVEDAFYTLVREIRQHKLRKLNPPDESGPGCMNCKCVIS\"\n    seq_len = len(sequence)\n    print(f\"📝 示例序列:\")\n    print(f\"   长度: {seq_len} 氨基酸\")", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "calculate_pssm_demo", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def calculate_pssm_demo(sequences: List[str], seq_len: int) -> np.ndarray:\n    \"\"\"演示PSSM计算\"\"\"\n    aa_to_idx = {aa: i for i, aa in enumerate(\"ACDEFGHIKLMNPQRSTVWY\")}\n    pssm = np.zeros((seq_len, 20))\n    for pos in range(seq_len):\n        aa_counts = np.zeros(20)\n        total_count = 0\n        for seq in sequences:\n            if pos < len(seq) and seq[pos] in aa_to_idx:\n                aa_counts[aa_to_idx[seq[pos]]] += 1", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "calculate_conservation_demo", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def calculate_conservation_demo(sequences: List[str]) -> np.ndarray:\n    \"\"\"演示保守性计算\"\"\"\n    if not sequences:\n        return np.zeros(1)\n    seq_len = len(sequences[0])\n    conservation = np.zeros(seq_len)\n    for pos in range(seq_len):\n        aa_counts = {}\n        for seq in sequences:\n            if pos < len(seq):", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "analyze_single_sequence_case", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def analyze_single_sequence_case():\n    \"\"\"分析单序列情况下的特征质量\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"单序列情况下的特征质量分析\")\n    print(\"=\" * 80)\n    print(\"🔍 当前情况分析:\")\n    print(\"   由于没有HHblits数据库，MSA只包含查询序列本身\")\n    print(\"   这意味着每个位置只有一种氨基酸\")\n    print(f\"\\n📊 单序列PSSM特征:\")\n    print(f\"   每个位置的PSSM向量:\")", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "compare_extraction_scenarios", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def compare_extraction_scenarios():\n    \"\"\"对比不同提取场景\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"不同MSA特征提取场景对比\")\n    print(\"=\" * 80)\n    scenarios = [\n        {\n            \"场景\": \"MSA Transformer成功\",\n            \"输入\": \"多序列MSA + 深度学习模型\",\n            \"21维核心特征\": \"不适用（直接768维深度特征）\",", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "verify_feature_extraction_code", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def verify_feature_extraction_code():\n    \"\"\"验证特征提取代码的正确性\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"特征提取代码正确性验证\")\n    print(\"=\" * 80)\n    print(\"📋 代码流程验证:\")\n    print(f\"\\n1. 错误捕获机制:\")\n    print(f\"   try:\")\n    print(f\"       # MSA Transformer特征提取\")\n    print(f\"   except Exception as e:\")", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "description": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 分析错误影响\n    analyze_msa_feature_extraction_error()\n    # 演示特征提取\n    results = demonstrate_traditional_feature_extraction()\n    # 分析单序列情况\n    analyze_single_sequence_case()\n    # 对比不同场景\n    compare_extraction_scenarios()", "detail": "protein2vector.attention_fusion.msa_feature_extraction_analysis", "documentation": {}}, {"label": "KcatDataProcessor", "kind": 6, "importPath": "protein2vector.attention_fusion.process_kcat_data", "description": "protein2vector.attention_fusion.process_kcat_data", "peekOfCode": "class KcatDataProcessor:\n    def __init__(self):\n        self.input_file = \"KcatData_20250625.xlsx\"\n        self.output_file = \"kcatData_20250625.csv\"\n        self.batch_size = 10  # 批量处理大小，避免内存溢出\n        self.checkpoint_interval = 100  # 每100条保存一次检查点\n    def load_data(self):\n        \"\"\"加载KcatData数据\"\"\"\n        logger.info(\"加载KcatData_20250625.xlsx文件...\")\n        if not os.path.exists(self.input_file):", "detail": "protein2vector.attention_fusion.process_kcat_data", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.process_kcat_data", "description": "protein2vector.attention_fusion.process_kcat_data", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"KcatData_20250625.xlsx 处理工具\")\n    print(\"使用attention fusion方法提取蛋白质序列特征\")\n    print(\"=\" * 60)\n    # 创建处理器\n    processor = KcatDataProcessor()\n    # 处理数据\n    try:\n        processor.process_kcat_data()", "detail": "protein2vector.attention_fusion.process_kcat_data", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.process_kcat_data", "description": "protein2vector.attention_fusion.process_kcat_data", "peekOfCode": "logger = logging.getLogger(__name__)\nclass KcatDataProcessor:\n    def __init__(self):\n        self.input_file = \"KcatData_20250625.xlsx\"\n        self.output_file = \"kcatData_20250625.csv\"\n        self.batch_size = 10  # 批量处理大小，避免内存溢出\n        self.checkpoint_interval = 100  # 每100条保存一次检查点\n    def load_data(self):\n        \"\"\"加载KcatData数据\"\"\"\n        logger.info(\"加载KcatData_20250625.xlsx文件...\")", "detail": "protein2vector.attention_fusion.process_kcat_data", "documentation": {}}, {"label": "setup_models", "kind": 2, "importPath": "protein2vector.attention_fusion.process_km_data", "description": "protein2vector.attention_fusion.process_km_data", "peekOfCode": "def setup_models():\n    \"\"\"初始化模型\"\"\"\n    logger.info(\"初始化attention_fusion模型...\")\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    logger.info(f\"使用设备: {device}\")\n    # 初始化特征提取器\n    extractor = ProteinFeatureExtractor(\n        esm_model_name=\"facebook/esm2_t33_650M_UR50D\",\n        msa_model_name=\"facebook/esm_msa1b_t12_100M_UR50S\"\n    )", "detail": "protein2vector.attention_fusion.process_km_data", "documentation": {}}, {"label": "extract_fusion_features", "kind": 2, "importPath": "protein2vector.attention_fusion.process_km_data", "description": "protein2vector.attention_fusion.process_km_data", "peekOfCode": "def extract_fusion_features(sequence, extractor, model, device):\n    \"\"\"提取单个序列的融合特征\"\"\"\n    try:\n        # 检查序列长度\n        if len(sequence) > 1000:\n            logger.warning(f\"序列长度 {len(sequence)} 超过1000，截取前1000个氨基酸\")\n            sequence = sequence[:1000]\n        # 提取融合特征\n        results = model.extract_and_fuse(sequence, extractor)\n        # 获取融合特征 (batch_size, seq_len, hidden_dim)", "detail": "protein2vector.attention_fusion.process_km_data", "documentation": {}}, {"label": "process_batch", "kind": 2, "importPath": "protein2vector.attention_fusion.process_km_data", "description": "protein2vector.attention_fusion.process_km_data", "peekOfCode": "def process_batch(sequences, ids, extractor, model, device, batch_size=1):\n    \"\"\"批量处理序列\"\"\"\n    results = []\n    for i in tqdm(range(0, len(sequences), batch_size), desc=\"处理序列\"):\n        batch_sequences = sequences[i:i+batch_size]\n        batch_ids = ids[i:i+batch_size]\n        for seq_id, sequence in zip(batch_ids, batch_sequences):\n            # 检查序列是否有效\n            if pd.isna(sequence) or len(sequence) < 8:\n                logger.warning(f\"序列 {seq_id} 无效，跳过\")", "detail": "protein2vector.attention_fusion.process_km_data", "documentation": {}}, {"label": "save_intermediate_results", "kind": 2, "importPath": "protein2vector.attention_fusion.process_km_data", "description": "protein2vector.attention_fusion.process_km_data", "peekOfCode": "def save_intermediate_results(results, output_path):\n    \"\"\"保存中间结果\"\"\"\n    intermediate_path = output_path.replace('.csv', '_intermediate.pkl')\n    with open(intermediate_path, 'wb') as f:\n        pickle.dump(results, f)\n    logger.info(f\"中间结果已保存到: {intermediate_path}\")\ndef load_intermediate_results(output_path):\n    \"\"\"加载中间结果\"\"\"\n    intermediate_path = output_path.replace('.csv', '_intermediate.pkl')\n    if os.path.exists(intermediate_path):", "detail": "protein2vector.attention_fusion.process_km_data", "documentation": {}}, {"label": "load_intermediate_results", "kind": 2, "importPath": "protein2vector.attention_fusion.process_km_data", "description": "protein2vector.attention_fusion.process_km_data", "peekOfCode": "def load_intermediate_results(output_path):\n    \"\"\"加载中间结果\"\"\"\n    intermediate_path = output_path.replace('.csv', '_intermediate.pkl')\n    if os.path.exists(intermediate_path):\n        with open(intermediate_path, 'rb') as f:\n            results = pickle.load(f)\n        logger.info(f\"从中间结果恢复: {len(results)} 个序列\")\n        return results\n    return None\ndef main():", "detail": "protein2vector.attention_fusion.process_km_data", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.process_km_data", "description": "protein2vector.attention_fusion.process_km_data", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    logger.info(\"开始处理Km_Data.xlsx文件\")\n    # 文件路径\n    input_file = \"../20250623_Data/Km_Data.xlsx\"\n    output_dir = \"../20250623_Data\"\n    output_file = os.path.join(output_dir, \"Km_Data_fusion_vector.csv\")\n    # 检查输入文件\n    if not os.path.exists(input_file):\n        logger.error(f\"输入文件不存在: {input_file}\")", "detail": "protein2vector.attention_fusion.process_km_data", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.process_km_data", "description": "protein2vector.attention_fusion.process_km_data", "peekOfCode": "logger = logging.getLogger(__name__)\ndef setup_models():\n    \"\"\"初始化模型\"\"\"\n    logger.info(\"初始化attention_fusion模型...\")\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    logger.info(f\"使用设备: {device}\")\n    # 初始化特征提取器\n    extractor = ProteinFeatureExtractor(\n        esm_model_name=\"facebook/esm2_t33_650M_UR50D\",\n        msa_model_name=\"facebook/esm_msa1b_t12_100M_UR50S\"", "detail": "protein2vector.attention_fusion.process_km_data", "documentation": {}}, {"label": "BatchProcessor", "kind": 6, "importPath": "protein2vector.attention_fusion.process_km_data_batch", "description": "protein2vector.attention_fusion.process_km_data_batch", "peekOfCode": "class BatchProcessor:\n    def __init__(self, batch_size=25):\n        self.batch_size = batch_size\n        self.output_file = \"../20250623_Data/Km_Data_fusion_vector.csv\"\n        self.progress_file = \"../20250623_Data/processing_progress.json\"\n        self.device = None\n        self.extractor = None\n        self.model = None\n    def setup_models(self):\n        \"\"\"初始化模型\"\"\"", "detail": "protein2vector.attention_fusion.process_km_data_batch", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.process_km_data_batch", "description": "protein2vector.attention_fusion.process_km_data_batch", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 创建处理器\n    processor = BatchProcessor(batch_size=25)  # 每25个序列保存一次\n    # 处理所有数据\n    processor.process_all_data()\nif __name__ == \"__main__\":\n    main()", "detail": "protein2vector.attention_fusion.process_km_data_batch", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.process_km_data_batch", "description": "protein2vector.attention_fusion.process_km_data_batch", "peekOfCode": "logger = logging.getLogger(__name__)\nclass BatchProcessor:\n    def __init__(self, batch_size=25):\n        self.batch_size = batch_size\n        self.output_file = \"../20250623_Data/Km_Data_fusion_vector.csv\"\n        self.progress_file = \"../20250623_Data/processing_progress.json\"\n        self.device = None\n        self.extractor = None\n        self.model = None\n    def setup_models(self):", "detail": "protein2vector.attention_fusion.process_km_data_batch", "documentation": {}}, {"label": "YangDataProcessor", "kind": 6, "importPath": "protein2vector.attention_fusion.process_yang_data_architectural", "description": "protein2vector.attention_fusion.process_yang_data_architectural", "peekOfCode": "class YangDataProcessor:\n    def __init__(self):\n        self.input_file = \"Yang_Data.xlsx\"\n        self.output_file = \"architectural_fusion.csv\"\n        self.device = None\n        self.extractor = None\n        self.model = None\n    def setup_models(self):\n        \"\"\"初始化模型\"\"\"\n        logger.info(\"初始化attention_fusion模型...\")", "detail": "protein2vector.attention_fusion.process_yang_data_architectural", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.process_yang_data_architectural", "description": "protein2vector.attention_fusion.process_yang_data_architectural", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 创建处理器\n    processor = YangDataProcessor()\n    # 处理数据\n    processor.process_yang_data()\nif __name__ == \"__main__\":\n    main()", "detail": "protein2vector.attention_fusion.process_yang_data_architectural", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.process_yang_data_architectural", "description": "protein2vector.attention_fusion.process_yang_data_architectural", "peekOfCode": "logger = logging.getLogger(__name__)\nclass YangDataProcessor:\n    def __init__(self):\n        self.input_file = \"Yang_Data.xlsx\"\n        self.output_file = \"architectural_fusion.csv\"\n        self.device = None\n        self.extractor = None\n        self.model = None\n    def setup_models(self):\n        \"\"\"初始化模型\"\"\"", "detail": "protein2vector.attention_fusion.process_yang_data_architectural", "documentation": {}}, {"label": "explain_pssm_dimensions", "kind": 2, "importPath": "protein2vector.attention_fusion.pssm_dimension_analysis", "description": "protein2vector.attention_fusion.pssm_dimension_analysis", "peekOfCode": "def explain_pssm_dimensions():\n    \"\"\"详细解释PSSM矩阵的维度结构\"\"\"\n    print(\"=\" * 80)\n    print(\"PSSM矩阵维度结构详细分析\")\n    print(\"=\" * 80)\n    # 示例参数\n    protein_length = 300  # 蛋白质长度\n    num_amino_acids = 20  # 标准氨基酸数量\n    print(f\"🧬 示例蛋白质:\")\n    print(f\"   长度: {protein_length} 个氨基酸残基\")", "detail": "protein2vector.attention_fusion.pssm_dimension_analysis", "documentation": {}}, {"label": "demonstrate_pssm_structure", "kind": 2, "importPath": "protein2vector.attention_fusion.pssm_dimension_analysis", "description": "protein2vector.attention_fusion.pssm_dimension_analysis", "peekOfCode": "def demonstrate_pssm_structure():\n    \"\"\"演示PSSM矩阵的具体结构\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"PSSM矩阵结构演示\")\n    print(\"=\" * 80)\n    # 氨基酸列表\n    amino_acids = list(\"ACDEFGHIKLMNPQRSTVWY\")\n    print(f\"20种标准氨基酸: {' '.join(amino_acids)}\")\n    # 示例：300氨基酸蛋白质的前5个位置\n    protein_length = 300", "detail": "protein2vector.attention_fusion.pssm_dimension_analysis", "documentation": {}}, {"label": "clarify_common_misconception", "kind": 2, "importPath": "protein2vector.attention_fusion.pssm_dimension_analysis", "description": "protein2vector.attention_fusion.pssm_dimension_analysis", "peekOfCode": "def clarify_common_misconception():\n    \"\"\"澄清常见误解\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"❌ 常见误解 vs ✅ 正确理解\")\n    print(\"=\" * 80)\n    print(\"❌ 错误理解:\")\n    print(\"   '300氨基酸蛋白质的20维PSSM是300个位置的平均值'\")\n    print(\"   → 这会丢失位置特异性信息！\")\n    print(\"\\n✅ 正确理解:\")\n    print(\"   '300氨基酸蛋白质的PSSM是 (300, 20) 矩阵'\")", "detail": "protein2vector.attention_fusion.pssm_dimension_analysis", "documentation": {}}, {"label": "explain_integration_process", "kind": 2, "importPath": "protein2vector.attention_fusion.pssm_dimension_analysis", "description": "protein2vector.attention_fusion.pssm_dimension_analysis", "peekOfCode": "def explain_integration_process():\n    \"\"\"解释PSSM如何整合到768维特征中\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"PSSM整合到768维特征的过程\")\n    print(\"=\" * 80)\n    protein_length = 300\n    print(f\"🔄 整合过程:\")\n    print(f\"   1. 计算PSSM矩阵: ({protein_length}, 20)\")\n    print(f\"   2. 计算保守性分数: ({protein_length}, 1)\")\n    print(f\"   3. 随机填充: ({protein_length}, 747)\")", "detail": "protein2vector.attention_fusion.pssm_dimension_analysis", "documentation": {}}, {"label": "demonstrate_real_example", "kind": 2, "importPath": "protein2vector.attention_fusion.pssm_dimension_analysis", "description": "protein2vector.attention_fusion.pssm_dimension_analysis", "peekOfCode": "def demonstrate_real_example():\n    \"\"\"演示真实的PSSM计算示例\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"真实PSSM计算示例\")\n    print(\"=\" * 80)\n    # 模拟MSA序列\n    msa_sequences = [\n        \"MKLLVL\",  # 查询序列\n        \"MKLLVL\",  # 相同序列\n        \"MKLLVL\",  # 相同序列  ", "detail": "protein2vector.attention_fusion.pssm_dimension_analysis", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.pssm_dimension_analysis", "description": "protein2vector.attention_fusion.pssm_dimension_analysis", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 解释PSSM维度\n    explain_pssm_dimensions()\n    # 演示PSSM结构\n    demonstrate_pssm_structure()\n    # 澄清误解\n    clarify_common_misconception()\n    # 解释整合过程\n    explain_integration_process()", "detail": "protein2vector.attention_fusion.pssm_dimension_analysis", "documentation": {}}, {"label": "clear_existing_models", "kind": 2, "importPath": "protein2vector.attention_fusion.reset_msa_model", "description": "protein2vector.attention_fusion.reset_msa_model", "peekOfCode": "def clear_existing_models():\n    \"\"\"清理现有的MSA模型缓存\"\"\"\n    print(\"[CLEAN] 清理现有MSA模型缓存...\")\n    # 清理torch hub缓存\n    torch_cache = Path.home() / \".cache\" / \"torch\" / \"hub\" / \"checkpoints\"\n    msa_files = [\n        \"esm_msa1b_t12_100M_UR50S.pt\",\n        \"esm_msa1b_t12_100M_UR50S-contact-regression.pt\"\n    ]\n    for file in msa_files:", "detail": "protein2vector.attention_fusion.reset_msa_model", "documentation": {}}, {"label": "download_facebook_msa_model", "kind": 2, "importPath": "protein2vector.attention_fusion.reset_msa_model", "description": "protein2vector.attention_fusion.reset_msa_model", "peekOfCode": "def download_facebook_msa_model():\n    \"\"\"重新下载Facebook官方MSA模型\"\"\"\n    print(\"\\n[DOWNLOAD] 重新下载Facebook官方MSA模型...\")\n    try:\n        import esm\n        print(\"使用fair-esm库下载模型...\")\n        # 这会自动下载Facebook官方模型\n        model, alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()\n        print(\"[SUCCESS] Facebook MSA模型下载成功!\")\n        print(f\"模型参数量: {sum(p.numel() for p in model.parameters()):,}\")", "detail": "protein2vector.attention_fusion.reset_msa_model", "documentation": {}}, {"label": "test_msa_model", "kind": 2, "importPath": "protein2vector.attention_fusion.reset_msa_model", "description": "protein2vector.attention_fusion.reset_msa_model", "peekOfCode": "def test_msa_model():\n    \"\"\"测试MSA模型是否正常工作\"\"\"\n    print(\"\\n🧪 测试MSA模型...\")\n    try:\n        from feature_extractor import ProteinFeatureExtractor\n        print(\"初始化特征提取器...\")\n        extractor = ProteinFeatureExtractor()\n        print(\"测试序列特征提取...\")\n        sequence = \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\"\n        features = extractor.extract_features(sequence)", "detail": "protein2vector.attention_fusion.reset_msa_model", "documentation": {}}, {"label": "show_model_info", "kind": 2, "importPath": "protein2vector.attention_fusion.reset_msa_model", "description": "protein2vector.attention_fusion.reset_msa_model", "peekOfCode": "def show_model_info():\n    \"\"\"显示模型信息\"\"\"\n    print(\"\\n[INFO] 模型信息:\")\n    print(\"=\" * 50)\n    # ESM-2模型\n    esm2_dir = Path.home() / \".cache\" / \"huggingface\" / \"hub\" / \"models--facebook--esm2_t33_650M_UR50D\"\n    if esm2_dir.exists():\n        print(\"[PASS] ESM-2模型: 已安装\")\n    else:\n        print(\"[FAIL] ESM-2模型: 未安装\")", "detail": "protein2vector.attention_fusion.reset_msa_model", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.reset_msa_model", "description": "protein2vector.attention_fusion.reset_msa_model", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"Facebook MSA模型重置工具\")\n    print(\"=\" * 50)\n    # 显示当前状态\n    show_model_info()\n    # 询问用户是否继续\n    print(\"\\n⚠️  这将删除现有的MSA模型缓存并重新下载Facebook官方模型\")\n    response = input(\"是否继续? (y/N): \").strip().lower()\n    if response not in ['y', 'yes', '是']:", "detail": "protein2vector.attention_fusion.reset_msa_model", "documentation": {}}, {"label": "seq2attention", "kind": 2, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "def seq2attention(sequence: str, \n                 return_numpy: bool = True,\n                 verbose: bool = False) -> Union[np.ndarray, torch.Tensor]:\n    \"\"\"\n    将蛋白质序列转换为attention融合特征向量\n    Args:\n        sequence (str): 蛋白质序列（单字母氨基酸代码）\n        return_numpy (bool): 是否返回numpy数组，默认True。如果False则返回torch.Tensor\n        verbose (bool): 是否显示详细日志信息，默认False\n    Returns:", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "batch_seq2attention", "kind": 2, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "def batch_seq2attention(sequences: List[str], \n                       return_numpy: bool = True,\n                       verbose: bool = False) -> Union[List[np.ndarray], List[torch.Tensor]]:\n    \"\"\"\n    批量将蛋白质序列转换为attention融合特征向量\n    Args:\n        sequences (List[str]): 蛋白质序列列表\n        return_numpy (bool): 是否返回numpy数组，默认True\n        verbose (bool): 是否显示详细日志信息，默认False\n    Returns:", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "get_model_info", "kind": 2, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "def get_model_info() -> dict:\n    \"\"\"\n    获取模型信息\n    Returns:\n        dict: 包含模型配置信息的字典\n    \"\"\"\n    return {\n        \"esm_model\": \"facebook/esm2_t33_650M_UR50D\",\n        \"msa_model\": \"facebook/esm_msa1b_t12_100M_UR50S\",\n        \"esm_dim\": 1280,", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "logger = logging.getLogger(__name__)\n# 全局变量，用于缓存模型\n_extractor = None\n_model = None\n_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "_extractor", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "_extractor = None\n_model = None\n_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化\n    logger.info(\"初始化attention fusion模型...\")\n    # 设置设备", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "_model", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "_model = None\n_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化\n    logger.info(\"初始化attention fusion模型...\")\n    # 设置设备\n    _device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "_device", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化\n    logger.info(\"初始化attention fusion模型...\")\n    # 设置设备\n    _device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    logger.info(f\"使用设备: {_device}\")", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "show_model_structure", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_models", "description": "protein2vector.attention_fusion.setup_models", "peekOfCode": "def show_model_structure():\n    \"\"\"显示需要的模型目录结构\"\"\"\n    cache_dir = Path.home() / \".cache\" / \"huggingface\" / \"hub\"\n    print(\"=\" * 80)\n    print(\"Attention Fusion 项目模型设置指南\")\n    print(\"=\" * 80)\n    print(f\"\\n📁 HuggingFace缓存目录: {cache_dir}\")\n    print(\"\\n🔧 需要下载的模型:\")\n    models = [\n        {", "detail": "protein2vector.attention_fusion.setup_models", "documentation": {}}, {"label": "check_models", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_models", "description": "protein2vector.attention_fusion.setup_models", "peekOfCode": "def check_models():\n    \"\"\"检查模型是否已正确安装\"\"\"\n    print(\"\\n[CHECK] 检查模型安装状态...\")\n    try:\n        # 激活conda环境并检查\n        import subprocess\n        result = subprocess.run([\n            \"conda\", \"run\", \"-n\", \"attention_fusion\", \"python\", \"-c\",\n            \"\"\"\nimport torch", "detail": "protein2vector.attention_fusion.setup_models", "documentation": {}}, {"label": "create_download_script", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_models", "description": "protein2vector.attention_fusion.setup_models", "peekOfCode": "def create_download_script():\n    \"\"\"创建自动下载脚本\"\"\"\n    script_path = Path(\"download_models.py\")\n    download_script = '''#!/usr/bin/env python3\n\"\"\"\n自动下载attention_fusion所需的模型\n\"\"\"\nimport os\nimport sys\nfrom pathlib import Path", "detail": "protein2vector.attention_fusion.setup_models", "documentation": {}}, {"label": "download_models", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_models", "description": "protein2vector.attention_fusion.setup_models", "peekOfCode": "def download_models():\n    \"\"\"下载所需模型\"\"\"\n    try:\n        from huggingface_hub import snapshot_download\n    except ImportError:\n        print(\"请先安装huggingface-hub: pip install huggingface-hub\")\n        return False\n    # 模型列表\n    models = [\n        \"facebook/esm2_t33_650M_UR50D\",", "detail": "protein2vector.attention_fusion.setup_models", "documentation": {}}, {"label": "cache_dir", "kind": 5, "importPath": "protein2vector.attention_fusion.setup_models", "description": "protein2vector.attention_fusion.setup_models", "peekOfCode": "cache_dir = \"/home/<USER>/.cache/huggingface/hub\"\nos.makedirs(cache_dir, exist_ok=True)\nmodels = [\n    \"facebook/esm2_t33_650M_UR50D\",\n    \"facebook/esm_msa1b_t12_100M_UR50S\"\n]\nfor model_name in models:\n    print(f\"下载 {model_name}...\")\n    try:\n        snapshot_download(", "detail": "protein2vector.attention_fusion.setup_models", "documentation": {}}, {"label": "models", "kind": 5, "importPath": "protein2vector.attention_fusion.setup_models", "description": "protein2vector.attention_fusion.setup_models", "peekOfCode": "models = [\n    \"facebook/esm2_t33_650M_UR50D\",\n    \"facebook/esm_msa1b_t12_100M_UR50S\"\n]\nfor model_name in models:\n    print(f\"下载 {model_name}...\")\n    try:\n        snapshot_download(\n            repo_id=model_name,\n            cache_dir=cache_dir,", "detail": "protein2vector.attention_fusion.setup_models", "documentation": {}}, {"label": "models_to_check", "kind": 5, "importPath": "protein2vector.attention_fusion.setup_models", "description": "protein2vector.attention_fusion.setup_models", "peekOfCode": "models_to_check = [\n    'facebook/esm2_t33_650M_UR50D',\n    'facebook/esm_msa1b_t12_100M_UR50S'\n]\nfor model_name in models_to_check:\n    try:\n        print(f'检查 {model_name}...')\n        tokenizer = EsmTokenizer.from_pretrained(model_name, local_files_only=True)\n        model = EsmModel.from_pretrained(model_name, local_files_only=True)\n        print(f'[PASS] {model_name} 可用')", "detail": "protein2vector.attention_fusion.setup_models", "documentation": {}}, {"label": "setup_msa_model_structure", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_msa_model", "description": "protein2vector.attention_fusion.setup_msa_model", "peekOfCode": "def setup_msa_model_structure():\n    \"\"\"设置MSA模型的目录结构\"\"\"\n    cache_dir = Path.home() / \".cache\" / \"huggingface\" / \"hub\"\n    model_dir = cache_dir / \"models--katielink--esm_msa1b_t12_100M_UR50S\"\n    print(\"=\" * 80)\n    print(\"MSA Transformer模型设置指南\")\n    print(\"=\" * 80)\n    print(f\"\\n📁 目标目录: {model_dir}\")\n    if model_dir.exists():\n        print(\"✅ MSA模型目录已存在\")", "detail": "protein2vector.attention_fusion.setup_msa_model", "documentation": {}}, {"label": "create_manual_setup_guide", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_msa_model", "description": "protein2vector.attention_fusion.setup_msa_model", "peekOfCode": "def create_manual_setup_guide():\n    \"\"\"创建手动设置指南\"\"\"\n    cache_dir = Path.home() / \".cache\" / \"huggingface\" / \"hub\"\n    model_dir = cache_dir / \"models--katielink--esm_msa1b_t12_100M_UR50S\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"手动下载和设置指南\")\n    print(\"=\" * 80)\n    print(\"\\n🔗 HuggingFace模型页面:\")\n    print(\"https://huggingface.co/katielink/esm_msa1b_t12_100M_UR50S\")\n    print(\"\\n📥 需要下载的文件:\")", "detail": "protein2vector.attention_fusion.setup_msa_model", "documentation": {}}, {"label": "test_msa_model", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_msa_model", "description": "protein2vector.attention_fusion.setup_msa_model", "peekOfCode": "def test_msa_model():\n    \"\"\"测试MSA模型是否可用\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"测试MSA模型\")\n    print(\"=\" * 80)\n    try:\n        from transformers import EsmModel, EsmTokenizer\n        print(\"正在测试MSA Transformer加载...\")\n        tokenizer = EsmTokenizer.from_pretrained(\n            \"katielink/esm_msa1b_t12_100M_UR50S\",", "detail": "protein2vector.attention_fusion.setup_msa_model", "documentation": {}}, {"label": "show_current_status", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_msa_model", "description": "protein2vector.attention_fusion.setup_msa_model", "peekOfCode": "def show_current_status():\n    \"\"\"显示当前状态\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"当前状态\")\n    print(\"=\" * 80)\n    # 检查ESM-2模型\n    esm2_dir = Path.home() / \".cache\" / \"huggingface\" / \"hub\" / \"models--facebook--esm2_t33_650M_UR50D\"\n    if esm2_dir.exists():\n        print(\"[PASS] ESM-2模型已安装\")\n    else:", "detail": "protein2vector.attention_fusion.setup_msa_model", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.setup_msa_model", "description": "protein2vector.attention_fusion.setup_msa_model", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"MSA Transformer模型设置工具\")\n    # 显示当前状态\n    show_current_status()\n    # 检查MSA模型\n    if not setup_msa_model_structure():\n        print(\"\\n需要设置MSA模型...\")\n        snapshot_dir = create_manual_setup_guide()\n        if snapshot_dir:", "detail": "protein2vector.attention_fusion.setup_msa_model", "documentation": {}}, {"label": "test_small_batch", "kind": 2, "importPath": "protein2vector.attention_fusion.test_km_data", "description": "protein2vector.attention_fusion.test_km_data", "peekOfCode": "def test_small_batch():\n    \"\"\"测试处理前10条数据\"\"\"\n    logger.info(\"测试处理前10条数据\")\n    # 读取数据\n    input_file = \"../20250623_Data/Km_Data.xlsx\"\n    df = pd.read_excel(input_file)\n    # 取前10条数据\n    test_df = df[['ID', 'Sequence']].head(10).copy()\n    logger.info(f\"测试数据形状: {test_df.shape}\")\n    # 初始化模型", "detail": "protein2vector.attention_fusion.test_km_data", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.test_km_data", "description": "protein2vector.attention_fusion.test_km_data", "peekOfCode": "logger = logging.getLogger(__name__)\ndef test_small_batch():\n    \"\"\"测试处理前10条数据\"\"\"\n    logger.info(\"测试处理前10条数据\")\n    # 读取数据\n    input_file = \"../20250623_Data/Km_Data.xlsx\"\n    df = pd.read_excel(input_file)\n    # 取前10条数据\n    test_df = df[['ID', 'Sequence']].head(10).copy()\n    logger.info(f\"测试数据形状: {test_df.shape}\")", "detail": "protein2vector.attention_fusion.test_km_data", "documentation": {}}, {"label": "analyze_traditional_msa_features", "kind": 2, "importPath": "protein2vector.attention_fusion.traditional_msa_analysis", "description": "protein2vector.attention_fusion.traditional_msa_analysis", "peekOfCode": "def analyze_traditional_msa_features():\n    \"\"\"分析传统MSA特征的768维构成\"\"\"\n    print(\"=\" * 80)\n    print(\"传统MSA特征768维构成详细分析\")\n    print(\"=\" * 80)\n    # 模拟一个序列长度为100的蛋白质\n    seq_len = 100\n    msa_dim = 768  # 目标维度\n    print(f\"\\n📊 特征维度分解 (序列长度: {seq_len}):\")\n    print(\"-\" * 50)", "detail": "protein2vector.attention_fusion.traditional_msa_analysis", "documentation": {}}, {"label": "demonstrate_pssm_calculation", "kind": 2, "importPath": "protein2vector.attention_fusion.traditional_msa_analysis", "description": "protein2vector.attention_fusion.traditional_msa_analysis", "peekOfCode": "def demonstrate_pssm_calculation():\n    \"\"\"演示PSSM计算过程\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"PSSM计算过程演示\")\n    print(\"=\" * 80)\n    # 示例MSA序列\n    msa_sequences = [\n        \"ACDEFG\",  # 查询序列\n        \"ACDEFG\",  # 相同序列\n        \"ACDEFG\",  # 相同序列", "detail": "protein2vector.attention_fusion.traditional_msa_analysis", "documentation": {}}, {"label": "demonstrate_conservation_calculation", "kind": 2, "importPath": "protein2vector.attention_fusion.traditional_msa_analysis", "description": "protein2vector.attention_fusion.traditional_msa_analysis", "peekOfCode": "def demonstrate_conservation_calculation():\n    \"\"\"演示保守性计算过程\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"保守性分数计算演示\")\n    print(\"=\" * 80)\n    # 不同保守性的位置示例\n    examples = [\n        {\n            'name': '完全保守位置',\n            'amino_acids': ['A', 'A', 'A', 'A', 'A'],", "detail": "protein2vector.attention_fusion.traditional_msa_analysis", "documentation": {}}, {"label": "compare_with_msa_transformer", "kind": 2, "importPath": "protein2vector.attention_fusion.traditional_msa_analysis", "description": "protein2vector.attention_fusion.traditional_msa_analysis", "peekOfCode": "def compare_with_msa_transformer():\n    \"\"\"对比传统MSA特征与MSA Transformer特征\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"传统MSA特征 vs MSA Transformer特征对比\")\n    print(\"=\" * 80)\n    comparison = [\n        {\n            'aspect': '特征维度',\n            'traditional': '768维 (PSSM:20 + 保守性:1 + 填充:747)',\n            'transformer': '768维 (深度学习学习的表示)',", "detail": "protein2vector.attention_fusion.traditional_msa_analysis", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.traditional_msa_analysis", "description": "protein2vector.attention_fusion.traditional_msa_analysis", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 分析特征构成\n    feature_info = analyze_traditional_msa_features()\n    # 演示PSSM计算\n    demonstrate_pssm_calculation()\n    # 演示保守性计算\n    demonstrate_conservation_calculation()\n    # 对比分析\n    compare_with_msa_transformer()", "detail": "protein2vector.attention_fusion.traditional_msa_analysis", "documentation": {}}, {"label": "visualize_attention_weights", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def visualize_attention_weights(attention_weights: torch.Tensor, sequence: str,\n                              title: str = \"Attention Weights\", save_path: Optional[str] = None,\n                              figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化注意力权重矩阵\n    Args:\n        attention_weights: 注意力权重 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题\n        save_path: 保存路径", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "visualize_cross_attention_flow", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def visualize_cross_attention_flow(esm_to_msa_weights: torch.Tensor, \n                                  msa_to_esm_weights: torch.Tensor,\n                                  sequence: str, save_path: Optional[str] = None):\n    \"\"\"\n    可视化交叉注意力流\n    Args:\n        esm_to_msa_weights: ESM-2到MSA的注意力权重\n        msa_to_esm_weights: MSA到ESM-2的注意力权重\n        sequence: 蛋白质序列\n        save_path: 保存路径", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "analyze_attention_patterns", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def analyze_attention_patterns(attention_weights: List[Dict], sequence: str) -> Dict[str, float]:\n    \"\"\"\n    分析注意力模式\n    Args:\n        attention_weights: 注意力权重列表\n        sequence: 蛋白质序列\n    Returns:\n        注意力模式分析结果\n    \"\"\"\n    # 计算平均注意力权重", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "compute_feature_similarity", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def compute_feature_similarity(features1: torch.Tensor, features2: torch.Tensor, \n                             method: str = \"cosine\") -> Dict[str, float]:\n    \"\"\"\n    计算特征相似性\n    Args:\n        features1: 第一组特征\n        features2: 第二组特征\n        method: 相似性计算方法\n    Returns:\n        相似性分析结果", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "plot_feature_evolution", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def plot_feature_evolution(features_by_layer: List[torch.Tensor], sequence: str,\n                          save_path: Optional[str] = None):\n    \"\"\"\n    绘制特征在不同层的演化\n    Args:\n        features_by_layer: 不同层的特征列表\n        sequence: 蛋白质序列\n        save_path: 保存路径\n    \"\"\"\n    num_layers = len(features_by_layer)", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "save_attention_analysis", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def save_attention_analysis(results: Dict[str, torch.Tensor], save_path: str):\n    \"\"\"\n    保存注意力分析结果\n    Args:\n        results: 分析结果\n        save_path: 保存路径\n    \"\"\"\n    # 转换为numpy格式保存\n    results_np = {}\n    for key, value in results.items():", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "load_attention_analysis", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def load_attention_analysis(load_path: str) -> Dict:\n    \"\"\"\n    加载注意力分析结果\n    Args:\n        load_path: 文件路径\n    Returns:\n        分析结果字典\n    \"\"\"\n    data = np.load(load_path, allow_pickle=True)\n    results = {}", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "print_attention_summary", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def print_attention_summary(results: Dict[str, torch.Tensor]):\n    \"\"\"\n    打印注意力分析摘要\n    Args:\n        results: 模型输出结果\n    \"\"\"\n    print(\"=\" * 60)\n    print(\"ATTENTION FUSION ANALYSIS SUMMARY\")\n    print(\"=\" * 60)\n    if \"attention_weights\" in results:", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "validate_attention_fusion_input", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def validate_attention_fusion_input(esm_features: torch.Tensor, msa_features: Dict) -> bool:\n    \"\"\"\n    验证注意力融合模型的输入\n    Args:\n        esm_features: ESM-2特征\n        msa_features: MSA特征字典\n    Returns:\n        是否为有效输入\n    \"\"\"\n    try:", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "logger = logging.getLogger(__name__)\ndef visualize_attention_weights(attention_weights: torch.Tensor, sequence: str,\n                              title: str = \"Attention Weights\", save_path: Optional[str] = None,\n                              figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化注意力权重矩阵\n    Args:\n        attention_weights: 注意力权重 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "demo_basic_usage", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def demo_basic_usage():\n    \"\"\"演示基本使用流程\"\"\"\n    print(\"[DNA] Utils.py 基本使用演示\")\n    print(\"=\" * 50)\n    # 1. 初始化模型\n    print(\"1. 初始化模型...\")\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    extractor = ProteinFeatureExtractor()\n    model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)\n    model.to(device)", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "demo_input_validation", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def demo_input_validation():\n    \"\"\"演示输入验证功能\"\"\"\n    print(\"\\n[SEARCH] 输入验证演示\")\n    print(\"=\" * 30)\n    # 准备测试数据\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    # 有效输入\n    valid_esm = torch.randn(1, 50, 1280).to(device)\n    valid_msa = {\n        \"query_representation\": torch.randn(50, 768),", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "demo_attention_analysis", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def demo_attention_analysis():\n    \"\"\"演示注意力分析功能\"\"\"\n    print(\"\\n[CHART] 注意力分析演示\")\n    print(\"=\" * 30)\n    # 获取模型结果\n    results, sequence, esm_features, msa_features = demo_basic_usage()\n    # 1. 打印注意力摘要\n    print(\"1. 注意力分析摘要:\")\n    utils.print_attention_summary(results)\n    # 2. 详细注意力模式分析", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "demo_feature_similarity", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def demo_feature_similarity():\n    \"\"\"演示特征相似性分析\"\"\"\n    print(\"\\n🔗 特征相似性分析演示\")\n    print(\"=\" * 30)\n    # 获取模型结果\n    results, sequence, esm_features, msa_features = demo_basic_usage()\n    # 比较不同特征\n    fused_features = results[\"fused_features\"]\n    enhanced_esm = results[\"enhanced_esm_features\"]\n    enhanced_msa = results[\"enhanced_msa_features\"]", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "demo_visualization", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def demo_visualization():\n    \"\"\"演示可视化功能\"\"\"\n    print(\"\\n[ART] 可视化功能演示\")\n    print(\"=\" * 30)\n    # 获取模型结果\n    results, sequence, esm_features, msa_features = demo_basic_usage()\n    # 创建输出目录\n    output_dir = Path(\"visualization_output\")\n    output_dir.mkdir(exist_ok=True)\n    print(\"1. 注意力权重可视化...\")", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "demo_save_load", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def demo_save_load():\n    \"\"\"演示保存和加载功能\"\"\"\n    print(\"\\n💾 保存和加载演示\")\n    print(\"=\" * 30)\n    # 获取模型结果\n    results, sequence, esm_features, msa_features = demo_basic_usage()\n    # 创建输出目录\n    output_dir = Path(\"analysis_output\")\n    output_dir.mkdir(exist_ok=True)\n    # 保存分析结果", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "demo_advanced_analysis", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def demo_advanced_analysis():\n    \"\"\"演示高级分析功能\"\"\"\n    print(\"\\n🔬 高级分析演示\")\n    print(\"=\" * 30)\n    # 比较不同序列的注意力模式\n    sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"[:100]  # 截取前100个氨基酸\n    ]\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_demo", "description": "protein2vector.attention_fusion.utils_demo", "peekOfCode": "def main():\n    \"\"\"主演示函数\"\"\"\n    print(\"[TOOLBOX] Utils.py 完整功能演示\")\n    print(\"=\" * 60)\n    try:\n        # 基本使用演示\n        demo_basic_usage()\n        # 输入验证演示\n        demo_input_validation()\n        # 注意力分析演示", "detail": "protein2vector.attention_fusion.utils_demo", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.utils_simple_demo", "description": "protein2vector.attention_fusion.utils_simple_demo", "peekOfCode": "def main():\n    \"\"\"主演示函数\"\"\"\n    print(\"🧰 Utils.py 核心功能演示\")\n    print(\"=\" * 50)\n    # 1. 初始化模型\n    print(\"1. 初始化模型...\")\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    extractor = ProteinFeatureExtractor()\n    model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)\n    model.to(device)", "detail": "protein2vector.attention_fusion.utils_simple_demo", "documentation": {}}, {"label": "verify_esm2_results", "kind": 2, "importPath": "protein2vector.attention_fusion.verify_esm2_results", "description": "protein2vector.attention_fusion.verify_esm2_results", "peekOfCode": "def verify_esm2_results():\n    \"\"\"验证ESM-2演示结果\"\"\"\n    print(\"🔍 验证ESM-2演示结果\")\n    print(\"=\" * 50)\n    output_dir = Path(\"esm2_demo_output\")\n    # 1. 验证特征矩阵\n    print(\"\\n1. 验证特征矩阵...\")\n    feature_matrix = np.load(output_dir / \"esm2_feature_matrix.npy\")\n    print(f\"   特征矩阵形状: {feature_matrix.shape}\")\n    print(f\"   数据类型: {feature_matrix.dtype}\")", "detail": "protein2vector.attention_fusion.verify_esm2_results", "documentation": {}}, {"label": "demonstrate_feature_usage", "kind": 2, "importPath": "protein2vector.attention_fusion.verify_esm2_results", "description": "protein2vector.attention_fusion.verify_esm2_results", "peekOfCode": "def demonstrate_feature_usage():\n    \"\"\"演示如何使用生成的特征\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(\"🚀 特征使用演示\")\n    print(\"=\" * 50)\n    # 加载特征矩阵\n    feature_matrix = np.load(\"esm2_demo_output/esm2_feature_matrix.npy\")\n    print(\"💡 特征矩阵使用示例:\")\n    print(f\"   1. 获取特定位置的特征向量:\")\n    print(f\"      position_5_features = feature_matrix[4, :]  # 第5个位置\")", "detail": "protein2vector.attention_fusion.verify_esm2_results", "documentation": {}}, {"label": "analyze_warning_impact", "kind": 2, "importPath": "protein2vector.attention_fusion.warning_impact_analysis", "description": "protein2vector.attention_fusion.warning_impact_analysis", "peekOfCode": "def analyze_warning_impact():\n    \"\"\"分析MSA Transformer警告对结果的影响\"\"\"\n    print(\"=\" * 80)\n    print(\"MSA Transformer警告影响分析\")\n    print(\"=\" * 80)\n    print(\"⚠️  警告信息:\")\n    print(\"   'MSA Transformer extraction failed: 'list' object has no attribute 'strip'.\")\n    print(\"   Using traditional features.'\")\n    print(f\"\\n🔍 警告原因分析:\")\n    print(f\"   1. MSA Transformer模型加载成功\")", "detail": "protein2vector.attention_fusion.warning_impact_analysis", "documentation": {}}, {"label": "compare_feature_quality", "kind": 2, "importPath": "protein2vector.attention_fusion.warning_impact_analysis", "description": "protein2vector.attention_fusion.warning_impact_analysis", "peekOfCode": "def compare_feature_quality():\n    \"\"\"对比两种特征的质量\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"特征质量对比分析\")\n    print(\"=\" * 80)\n    # 特征质量评估矩阵\n    quality_metrics = {\n        \"特征维度\": {\n            \"MSA Transformer\": \"768维 (深度学习表示)\",\n            \"Traditional MSA\": \"768维 (PSSM+保守性+填充)\",", "detail": "protein2vector.attention_fusion.warning_impact_analysis", "documentation": {}}, {"label": "evaluate_fusion_impact", "kind": 2, "importPath": "protein2vector.attention_fusion.warning_impact_analysis", "description": "protein2vector.attention_fusion.warning_impact_analysis", "peekOfCode": "def evaluate_fusion_impact():\n    \"\"\"评估对attention fusion的影响\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"对Attention Fusion的影响评估\")\n    print(\"=\" * 80)\n    print(\"🔄 Fusion过程分析:\")\n    # 模拟特征质量\n    esm_quality = 1.0  # ESM-2特征质量不受影响\n    msa_transformer_quality = 1.0\n    traditional_msa_quality = 0.8", "detail": "protein2vector.attention_fusion.warning_impact_analysis", "documentation": {}}, {"label": "assess_downstream_impact", "kind": 2, "importPath": "protein2vector.attention_fusion.warning_impact_analysis", "description": "protein2vector.attention_fusion.warning_impact_analysis", "peekOfCode": "def assess_downstream_impact():\n    \"\"\"评估对下游任务的影响\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"下游任务影响评估\")\n    print(\"=\" * 80)\n    # 不同任务的影响程度\n    task_impacts = {\n        \"蛋白质分类\": {\n            \"影响程度\": \"轻微\",\n            \"预期性能\": \"95-98%\",", "detail": "protein2vector.attention_fusion.warning_impact_analysis", "documentation": {}}, {"label": "provide_recommendations", "kind": 2, "importPath": "protein2vector.attention_fusion.warning_impact_analysis", "description": "protein2vector.attention_fusion.warning_impact_analysis", "peekOfCode": "def provide_recommendations():\n    \"\"\"提供建议和解决方案\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"建议和解决方案\")\n    print(\"=\" * 80)\n    print(\"🚀 立即可行的方案:\")\n    print(\"   1. ✅ 继续使用当前结果\")\n    print(\"      - Traditional MSA特征质量足够好\")\n    print(\"      - 融合效果仍然显著优于单一特征\")\n    print(\"      - 计算效率更高\")", "detail": "protein2vector.attention_fusion.warning_impact_analysis", "documentation": {}}, {"label": "visualize_impact", "kind": 2, "importPath": "protein2vector.attention_fusion.warning_impact_analysis", "description": "protein2vector.attention_fusion.warning_impact_analysis", "peekOfCode": "def visualize_impact():\n    \"\"\"可视化影响程度\"\"\"\n    print(\"\\n\" + \"=\" * 80)\n    print(\"影响程度可视化\")\n    print(\"=\" * 80)\n    # 创建输出目录\n    output_dir = Path(\"warning_analysis_output\")\n    output_dir.mkdir(exist_ok=True)\n    # 特征质量对比\n    categories = ['特征丰富度', '生物学信息', '计算稳定性', '融合兼容性', '整体质量']", "detail": "protein2vector.attention_fusion.warning_impact_analysis", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "protein2vector.attention_fusion.warning_impact_analysis", "description": "protein2vector.attention_fusion.warning_impact_analysis", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    # 分析警告影响\n    analyze_warning_impact()\n    # 对比特征质量\n    compare_feature_quality()\n    # 评估融合影响\n    fusion_quality = evaluate_fusion_impact()\n    # 评估下游影响\n    assess_downstream_impact()", "detail": "protein2vector.attention_fusion.warning_impact_analysis", "documentation": {}}, {"label": "extract_molformer_features", "kind": 2, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "def extract_molformer_features(smiles):\n    try:\n        inputs = mol_tokenizer(smiles, return_tensors=\"pt\", padding=\"max_length\", truncation=True).to(device)\n        with torch.no_grad():\n            output = mol_model(**inputs)\n        # vector = output.last_hidden_state[:, 0, :].squeeze(0).cpu().numpy()\n        vector= output.pooler_output.cpu().numpy()\n        # print(vector[0][:2])\n        return vector, \"\"\n    except Exception as e:", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "extract_and_save_molformer_features", "kind": 2, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "def extract_and_save_molformer_features(input_path, output_path):\n    df = pd.read_excel(input_path)\n    print(f\"Loaded {len(df)} samples from {input_path}\")\n    output_rows = []\n    for _, row in tqdm(df.iterrows(), total=len(df)):\n        sample_id = row[\"ID\"]\n        seq = row.get(\"Sequence\", \"\")\n        smiles = row[\"Smiles\"]\n        # km_value = row[\"Log10_Km_Value\"]\n        vector, error = extract_molformer_features(smiles)", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "device", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\nprint(f\"Using device: {device}\")\n# 2. Load MolFormer\nmolformer_path = \"/usr/XML/database/MolFormer\"\nmol_tokenizer = AutoTokenizer.from_pretrained(molformer_path, trust_remote_code=True)\nmol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "molformer_path", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "molformer_path = \"/usr/XML/database/MolFormer\"\nmol_tokenizer = AutoTokenizer.from_pretrained(molformer_path, trust_remote_code=True)\nmol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):\n    try:\n        inputs = mol_tokenizer(smiles, return_tensors=\"pt\", padding=\"max_length\", truncation=True).to(device)\n        with torch.no_grad():", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "mol_tokenizer", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "mol_tokenizer = AutoTokenizer.from_pretrained(molformer_path, trust_remote_code=True)\nmol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):\n    try:\n        inputs = mol_tokenizer(smiles, return_tensors=\"pt\", padding=\"max_length\", truncation=True).to(device)\n        with torch.no_grad():\n            output = mol_model(**inputs)", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "mol_model", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):\n    try:\n        inputs = mol_tokenizer(smiles, return_tensors=\"pt\", padding=\"max_length\", truncation=True).to(device)\n        with torch.no_grad():\n            output = mol_model(**inputs)\n        # vector = output.last_hidden_state[:, 0, :].squeeze(0).cpu().numpy()", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "TransformerRegressor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class TransformerRegressor(nn.Module):\n    \"\"\"Transformer回归器 - 用于Kcat预测\"\"\"\n    def __init__(self, input_dim: int = 1280, d_model: int = 64, nhead: int = 4, \n                 num_layers: int = 2, dim_feedforward: int = 256, dropout: float = 0.1):\n        \"\"\"\n        初始化Transformer回归器\n        Args:\n            input_dim: 输入特征维度 (smiles_vector + sequence_vector)\n            d_model: Transformer模型维度\n            nhead: 注意力头数", "detail": "kcat_predict", "documentation": {}}, {"label": "OriginalTransformerRegressor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class OriginalTransformerRegressor(nn.Module):\n    \"\"\"原始Transformer回归器 - 兼容预训练模型\"\"\"\n    def __init__(self, input_dim: int = 1280, d_model: int = 64, nhead: int = 4,\n                 num_layers: int = 2, dim_feedforward: int = 256, dropout: float = 0.1):\n        \"\"\"\n        初始化原始Transformer回归器（兼容预训练模型结构）\n        \"\"\"\n        super(OriginalTransformerRegressor, self).__init__()\n        self.input_dim = input_dim\n        self.d_model = d_model", "detail": "kcat_predict", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器 - 简化版本\"\"\"\n    def __init__(self):\n        \"\"\"初始化特征提取器\"\"\"\n        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n        self._esm_model = None\n        self._esm_tokenizer = None\n        self._initialize_esm()\n    def _initialize_esm(self):\n        \"\"\"初始化ESM-2模型\"\"\"", "detail": "kcat_predict", "documentation": {}}, {"label": "SMILESFeatureExtractor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class SMILESFeatureExtractor:\n    \"\"\"SMILES分子特征提取器\"\"\"\n    def __init__(self):\n        \"\"\"初始化SMILES特征提取器\"\"\"\n        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n        self._mol_model = None\n        self._mol_tokenizer = None\n        self._initialize_molformer()\n    def _initialize_molformer(self):\n        \"\"\"初始化MolFormer模型\"\"\"", "detail": "kcat_predict", "documentation": {}}, {"label": "KcatPredictor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class KcatPredictor:\n    \"\"\"Kcat预测器主类\"\"\"\n    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):\n        \"\"\"\n        初始化Kcat预测器\n        Args:\n            model_path: 预训练模型路径\n            device: 计算设备 ('auto', 'cpu', 'cuda')\n        \"\"\"\n        # 设置设备", "detail": "kcat_predict", "documentation": {}}, {"label": "predict_kcat", "kind": 2, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "def predict_kcat(protein_sequence: str, smiles: str, verbose: bool = False) -> float:\n    \"\"\"\n    预测蛋白质-化合物相互作用的Kcat值\n    Args:\n        protein_sequence: 蛋白质序列（单字母氨基酸代码）\n        smiles: 化合物SMILES字符串\n        verbose: 是否显示详细信息\n    Returns:\n        预测的Log10_Kcat_Value\n    Example:", "detail": "kcat_predict", "documentation": {}}, {"label": "batch_predict_kcat", "kind": 2, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "def batch_predict_kcat(sequences: List[str], smiles_list: List[str], \n                      verbose: bool = False) -> List[float]:\n    \"\"\"\n    批量预测Kcat值\n    Args:\n        sequences: 蛋白质序列列表\n        smiles_list: SMILES字符串列表\n        verbose: 是否显示详细信息\n    Returns:\n        预测的Log10_Kcat_Value列表", "detail": "kcat_predict", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "logger = logging.getLogger(__name__)\n# 抑制警告\nwarnings.filterwarnings('ignore')\n# ==================== TransformerRegressor 模型定义 ====================\nclass TransformerRegressor(nn.Module):\n    \"\"\"Transformer回归器 - 用于Kcat预测\"\"\"\n    def __init__(self, input_dim: int = 1280, d_model: int = 64, nhead: int = 4, \n                 num_layers: int = 2, dim_feedforward: int = 256, dropout: float = 0.1):\n        \"\"\"\n        初始化Transformer回归器", "detail": "kcat_predict", "documentation": {}}, {"label": "_global_predictor", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "_global_predictor = None\ndef _get_predictor():\n    \"\"\"获取全局预测器实例\"\"\"\n    global _global_predictor\n    if _global_predictor is None:\n        _global_predictor = KcatPredictor()\n    return _global_predictor\n# ==================== 公共API ====================\ndef predict_kcat(protein_sequence: str, smiles: str, verbose: bool = False) -> float:\n    \"\"\"", "detail": "kcat_predict", "documentation": {}}]