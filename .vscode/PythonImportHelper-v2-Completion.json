[{"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "TransformerRegressor", "importPath": "utils.transformer_regressor", "description": "utils.transformer_regressor", "isExtraImport": true, "detail": "utils.transformer_regressor", "documentation": {}}, {"label": "torch.nn", "kind": 6, "isExtraImport": true, "importPath": "torch.nn", "description": "torch.nn", "detail": "torch.nn", "documentation": {}}, {"label": "torch.nn.functional", "kind": 6, "isExtraImport": true, "importPath": "torch.nn.functional", "description": "torch.nn.functional", "detail": "torch.nn.functional", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "math", "kind": 6, "isExtraImport": true, "importPath": "math", "description": "math", "detail": "math", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "rearrange", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "repeat", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "esm", "kind": 6, "isExtraImport": true, "importPath": "esm", "description": "esm", "detail": "esm", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "AutoTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "AutoModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "warnings", "kind": 6, "isExtraImport": true, "importPath": "warnings", "description": "warnings", "detail": "warnings", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "seaborn", "kind": 6, "isExtraImport": true, "importPath": "seaborn", "description": "seaborn", "detail": "seaborn", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "KcatPredictor", "kind": 6, "importPath": "kcat_part.predict", "description": "kcat_part.predict", "peekOfCode": "class KcatPredictor:\n    \"\"\"Kcat预测器\"\"\"\n    def __init__(self, model_path=None, device='auto'):\n        \"\"\"\n        初始化预测器\n        Args:\n            model_path: 模型文件路径，默认使用最佳模型\n            device: 设备选择 ('auto', 'cpu', 'cuda')\n        \"\"\"\n        # 设置设备", "detail": "kcat_part.predict", "documentation": {}}, {"label": "parse_vector_string", "kind": 2, "importPath": "kcat_part.predict", "description": "kcat_part.predict", "peekOfCode": "def parse_vector_string(vector_str):\n    \"\"\"解析向量字符串\"\"\"\n    try:\n        # 移除引号并解析为浮点数列表\n        vector_str = vector_str.strip('\"').strip(\"'\")\n        if vector_str.startswith('[') and vector_str.endswith(']'):\n            # 处理列表格式\n            vector_str = vector_str[1:-1]\n        vector_list = [float(x.strip()) for x in vector_str.split(',')]\n        return np.array(vector_list)", "detail": "kcat_part.predict", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "kcat_part.predict", "description": "kcat_part.predict", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    parser = argparse.ArgumentParser(description='Kcat预测工具')\n    parser.add_argument('--mode', choices=['single', 'batch'], default='single',\n                       help='预测模式: single(单样本) 或 batch(批量)')\n    parser.add_argument('--device', choices=['auto', 'cpu', 'cuda'], default='auto',\n                       help='设备选择: auto(自动), cpu, cuda')\n    parser.add_argument('--model', type=str, default=None,\n                       help='模型文件路径，默认使用最佳模型')\n    # 单样本预测参数", "detail": "kcat_part.predict", "documentation": {}}, {"label": "MultiHeadCrossAttention", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class MultiHeadCrossAttention(nn.Module):\n    \"\"\"多头交叉注意力模块\"\"\"\n    def __init__(self, query_dim: int, key_dim: int, value_dim: int, \n                 hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化多头交叉注意力\n        Args:\n            query_dim: 查询特征维度\n            key_dim: 键特征维度\n            value_dim: 值特征维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "CrossAttentionBlock", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class CrossAttentionBlock(nn.Module):\n    \"\"\"交叉注意力块\"\"\"\n    def __init__(self, esm_dim: int, msa_dim: int, hidden_dim: int, \n                 num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化交叉注意力块\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "MSAFeatureProcessor", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class MSAFeatureProcessor(nn.Module):\n    \"\"\"MSA特征处理器\"\"\"\n    def __init__(self, output_dim: int = 256):\n        \"\"\"\n        初始化MSA特征处理器\n        Args:\n            output_dim: 输出特征维度\n        \"\"\"\n        super().__init__()\n        self.output_dim = output_dim", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "kind": 6, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "class AttentionFusionModel(nn.Module):\n    \"\"\"注意力融合模型主类\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, hidden_dim: int = 512,\n                 num_blocks: int = 4, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化注意力融合模型\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.attention_fusion_model", "description": "protein2vector.attention_fusion.attention_fusion_model", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MultiHeadCrossAttention(nn.Module):\n    \"\"\"多头交叉注意力模块\"\"\"\n    def __init__(self, query_dim: int, key_dim: int, value_dim: int, \n                 hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化多头交叉注意力\n        Args:\n            query_dim: 查询特征维度\n            key_dim: 键特征维度", "detail": "protein2vector.attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "MSATransformerFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor", "description": "protein2vector.attention_fusion.feature_extractor", "peekOfCode": "class MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器 - 修复版本\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        self.model_name = model_name\n        # 尝试加载MSA模型\n        try:\n            logger.info(f\"Loading MSA Transformer model: {model_name}\")\n            # 首先尝试使用fair-esm库\n            try:", "detail": "protein2vector.attention_fusion.feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "protein2vector.attention_fusion.feature_extractor", "description": "protein2vector.attention_fusion.feature_extractor", "peekOfCode": "class ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器 - 修复版本\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\",\n                 msa_model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n            msa_model_name: MSA Transformer模型名称\n        \"\"\"", "detail": "protein2vector.attention_fusion.feature_extractor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.feature_extractor", "description": "protein2vector.attention_fusion.feature_extractor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器 - 修复版本\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        self.model_name = model_name\n        # 尝试加载MSA模型\n        try:\n            logger.info(f\"Loading MSA Transformer model: {model_name}\")\n            # 首先尝试使用fair-esm库", "detail": "protein2vector.attention_fusion.feature_extractor", "documentation": {}}, {"label": "seq2attention", "kind": 2, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "def seq2attention(sequence: str, \n                 return_numpy: bool = True,\n                 verbose: bool = False) -> Union[np.ndarray, torch.Tensor]:\n    \"\"\"\n    将蛋白质序列转换为attention融合特征向量\n    Args:\n        sequence (str): 蛋白质序列（单字母氨基酸代码）\n        return_numpy (bool): 是否返回numpy数组，默认True。如果False则返回torch.Tensor\n        verbose (bool): 是否显示详细日志信息，默认False\n    Returns:", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "batch_seq2attention", "kind": 2, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "def batch_seq2attention(sequences: List[str], \n                       return_numpy: bool = True,\n                       verbose: bool = False) -> Union[List[np.ndarray], List[torch.Tensor]]:\n    \"\"\"\n    批量将蛋白质序列转换为attention融合特征向量\n    Args:\n        sequences (List[str]): 蛋白质序列列表\n        return_numpy (bool): 是否返回numpy数组，默认True\n        verbose (bool): 是否显示详细日志信息，默认False\n    Returns:", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "get_model_info", "kind": 2, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "def get_model_info() -> dict:\n    \"\"\"\n    获取模型信息\n    Returns:\n        dict: 包含模型配置信息的字典\n    \"\"\"\n    return {\n        \"esm_model\": \"facebook/esm2_t33_650M_UR50D\",\n        \"msa_model\": \"facebook/esm_msa1b_t12_100M_UR50S\",\n        \"esm_dim\": 1280,", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "logger = logging.getLogger(__name__)\n# 全局变量，用于缓存模型\n_extractor = None\n_model = None\n_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "_extractor", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "_extractor = None\n_model = None\n_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化\n    logger.info(\"初始化attention fusion模型...\")\n    # 设置设备", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "_model", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "_model = None\n_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化\n    logger.info(\"初始化attention fusion模型...\")\n    # 设置设备\n    _device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "_device", "kind": 5, "importPath": "protein2vector.attention_fusion.seq2attention", "description": "protein2vector.attention_fusion.seq2attention", "peekOfCode": "_device = None\ndef _initialize_models():\n    \"\"\"初始化attention fusion模型（仅在首次调用时执行）\"\"\"\n    global _extractor, _model, _device\n    if _extractor is not None and _model is not None:\n        return  # 模型已经初始化\n    logger.info(\"初始化attention fusion模型...\")\n    # 设置设备\n    _device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    logger.info(f\"使用设备: {_device}\")", "detail": "protein2vector.attention_fusion.seq2attention", "documentation": {}}, {"label": "visualize_attention_weights", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def visualize_attention_weights(attention_weights: torch.Tensor, sequence: str,\n                              title: str = \"Attention Weights\", save_path: Optional[str] = None,\n                              figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化注意力权重矩阵\n    Args:\n        attention_weights: 注意力权重 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题\n        save_path: 保存路径", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "visualize_cross_attention_flow", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def visualize_cross_attention_flow(esm_to_msa_weights: torch.Tensor, \n                                  msa_to_esm_weights: torch.Tensor,\n                                  sequence: str, save_path: Optional[str] = None):\n    \"\"\"\n    可视化交叉注意力流\n    Args:\n        esm_to_msa_weights: ESM-2到MSA的注意力权重\n        msa_to_esm_weights: MSA到ESM-2的注意力权重\n        sequence: 蛋白质序列\n        save_path: 保存路径", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "analyze_attention_patterns", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def analyze_attention_patterns(attention_weights: List[Dict], sequence: str) -> Dict[str, float]:\n    \"\"\"\n    分析注意力模式\n    Args:\n        attention_weights: 注意力权重列表\n        sequence: 蛋白质序列\n    Returns:\n        注意力模式分析结果\n    \"\"\"\n    # 计算平均注意力权重", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "compute_feature_similarity", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def compute_feature_similarity(features1: torch.Tensor, features2: torch.Tensor, \n                             method: str = \"cosine\") -> Dict[str, float]:\n    \"\"\"\n    计算特征相似性\n    Args:\n        features1: 第一组特征\n        features2: 第二组特征\n        method: 相似性计算方法\n    Returns:\n        相似性分析结果", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "plot_feature_evolution", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def plot_feature_evolution(features_by_layer: List[torch.Tensor], sequence: str,\n                          save_path: Optional[str] = None):\n    \"\"\"\n    绘制特征在不同层的演化\n    Args:\n        features_by_layer: 不同层的特征列表\n        sequence: 蛋白质序列\n        save_path: 保存路径\n    \"\"\"\n    num_layers = len(features_by_layer)", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "save_attention_analysis", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def save_attention_analysis(results: Dict[str, torch.Tensor], save_path: str):\n    \"\"\"\n    保存注意力分析结果\n    Args:\n        results: 分析结果\n        save_path: 保存路径\n    \"\"\"\n    # 转换为numpy格式保存\n    results_np = {}\n    for key, value in results.items():", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "load_attention_analysis", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def load_attention_analysis(load_path: str) -> Dict:\n    \"\"\"\n    加载注意力分析结果\n    Args:\n        load_path: 文件路径\n    Returns:\n        分析结果字典\n    \"\"\"\n    data = np.load(load_path, allow_pickle=True)\n    results = {}", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "print_attention_summary", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def print_attention_summary(results: Dict[str, torch.Tensor]):\n    \"\"\"\n    打印注意力分析摘要\n    Args:\n        results: 模型输出结果\n    \"\"\"\n    print(\"=\" * 60)\n    print(\"ATTENTION FUSION ANALYSIS SUMMARY\")\n    print(\"=\" * 60)\n    if \"attention_weights\" in results:", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "validate_attention_fusion_input", "kind": 2, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "def validate_attention_fusion_input(esm_features: torch.Tensor, msa_features: Dict) -> bool:\n    \"\"\"\n    验证注意力融合模型的输入\n    Args:\n        esm_features: ESM-2特征\n        msa_features: MSA特征字典\n    Returns:\n        是否为有效输入\n    \"\"\"\n    try:", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "protein2vector.attention_fusion.utils", "description": "protein2vector.attention_fusion.utils", "peekOfCode": "logger = logging.getLogger(__name__)\ndef visualize_attention_weights(attention_weights: torch.Tensor, sequence: str,\n                              title: str = \"Attention Weights\", save_path: Optional[str] = None,\n                              figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化注意力权重矩阵\n    Args:\n        attention_weights: 注意力权重 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题", "detail": "protein2vector.attention_fusion.utils", "documentation": {}}, {"label": "extract_molformer_features", "kind": 2, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "def extract_molformer_features(smiles):\n    try:\n        # 使用模型的默认最大长度，或者设置一个合理的长度\n        max_length = getattr(mol_tokenizer, 'model_max_length', 512)\n        if max_length > 10000:  # 如果默认长度过大，使用512\n            max_length = 512\n        inputs = mol_tokenizer(\n            smiles,\n            return_tensors=\"pt\",\n            padding=\"max_length\",", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "extract_and_save_molformer_features", "kind": 2, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "def extract_and_save_molformer_features(input_path, output_path):\n    df = pd.read_excel(input_path)\n    print(f\"Loaded {len(df)} samples from {input_path}\")\n    output_rows = []\n    for _, row in tqdm(df.iterrows(), total=len(df)):\n        sample_id = row[\"ID\"]\n        seq = row.get(\"Sequence\", \"\")\n        smiles = row[\"Smiles\"]\n        # km_value = row[\"Log10_Km_Value\"]\n        vector, error = extract_molformer_features(smiles)", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "device", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\nprint(f\"Using device: {device}\")\n# 2. Load MolFormer\nmolformer_path = \"/usr/XML/database/MolFormer\"\nmol_tokenizer = AutoTokenizer.from_pretrained(molformer_path, trust_remote_code=True)\nmol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "molformer_path", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "molformer_path = \"/usr/XML/database/MolFormer\"\nmol_tokenizer = AutoTokenizer.from_pretrained(molformer_path, trust_remote_code=True)\nmol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):\n    try:\n        # 使用模型的默认最大长度，或者设置一个合理的长度\n        max_length = getattr(mol_tokenizer, 'model_max_length', 512)", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "mol_tokenizer", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "mol_tokenizer = AutoTokenizer.from_pretrained(molformer_path, trust_remote_code=True)\nmol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):\n    try:\n        # 使用模型的默认最大长度，或者设置一个合理的长度\n        max_length = getattr(mol_tokenizer, 'model_max_length', 512)\n        if max_length > 10000:  # 如果默认长度过大，使用512", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "mol_model", "kind": 5, "importPath": "smiles2vector.smiles_feature", "description": "smiles2vector.smiles_feature", "peekOfCode": "mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)\n# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)\nmol_model.eval()\n# 3. Molecule feature extraction\ndef extract_molformer_features(smiles):\n    try:\n        # 使用模型的默认最大长度，或者设置一个合理的长度\n        max_length = getattr(mol_tokenizer, 'model_max_length', 512)\n        if max_length > 10000:  # 如果默认长度过大，使用512\n            max_length = 512", "detail": "smiles2vector.smiles_feature", "documentation": {}}, {"label": "StrictProteinFeatureExtractor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class StrictProteinFeatureExtractor:\n    \"\"\"严格使用原始seq2attention的蛋白质特征提取器\"\"\"\n    def __init__(self):\n        \"\"\"初始化特征提取器\"\"\"\n        self.seq2attention = _import_seq2attention()\n        logger.info(\"成功导入原始seq2attention函数\")\n    def extract_sequence_features(self, sequence: str) -> np.ndarray:\n        \"\"\"\n        严格使用原始seq2attention提取蛋白质特征\n        Args:", "detail": "kcat_predict", "documentation": {}}, {"label": "StrictSMILESFeatureExtractor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class StrictSMILESFeatureExtractor:\n    \"\"\"严格使用原始smiles_feature的SMILES特征提取器\"\"\"\n    def __init__(self):\n        \"\"\"初始化SMILES特征提取器\"\"\"\n        self.extract_molformer_features = _import_smiles_feature()\n        logger.info(\"成功导入原始extract_molformer_features函数\")\n    def extract_smiles_features(self, smiles: str) -> np.ndarray:\n        \"\"\"\n        严格使用原始extract_molformer_features提取SMILES特征\n        Args:", "detail": "kcat_predict", "documentation": {}}, {"label": "StrictKcatPredictor", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class StrictKcatPredictor:\n    \"\"\"严格使用原始predict.py的Kcat预测器\"\"\"\n    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):\n        \"\"\"\n        初始化严格的Kcat预测器\n        Args:\n            model_path: 预训练模型路径\n            device: 计算设备\n        \"\"\"\n        try:", "detail": "kcat_predict", "documentation": {}}, {"label": "StrictKcatPredictorComplete", "kind": 6, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "class StrictKcatPredictorComplete:\n    \"\"\"严格使用所有原始组件的完整Kcat预测器\"\"\"\n    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):\n        \"\"\"\n        初始化严格的完整Kcat预测器\n        Args:\n            model_path: 预训练模型路径\n            device: 计算设备\n        \"\"\"\n        logger.info(\"初始化严格模式的Kcat预测器...\")", "detail": "kcat_predict", "documentation": {}}, {"label": "predict_kcat", "kind": 2, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "def predict_kcat(protein_sequence: str, smiles: str, verbose: bool = False) -> float:\n    \"\"\"\n    严格使用原始组件预测蛋白质-化合物相互作用的Kcat值\n    该函数严格使用以下原始组件，不做任何降级：\n    1. protein2vector/attention_fusion/seq2attention.py - ESM2+MSA attention fusion\n    2. smiles2vector/smiles_feature.py - MolFormer特征提取\n    3. kcat_part/predict.py - 最佳预训练模型\n    Args:\n        protein_sequence: 蛋白质序列（单字母氨基酸代码）\n        smiles: 化合物SMILES字符串", "detail": "kcat_predict", "documentation": {}}, {"label": "batch_predict_kcat", "kind": 2, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "def batch_predict_kcat(sequences: List[str], smiles_list: List[str],\n                      verbose: bool = False, batch_size: int = 64) -> List[float]:\n    \"\"\"\n    严格使用原始组件批量预测Kcat值\n    Args:\n        sequences: 蛋白质序列列表\n        smiles_list: SMILES字符串列表\n        verbose: 是否显示详细信息\n        batch_size: 批次大小\n    Returns:", "detail": "kcat_predict", "documentation": {}}, {"label": "get_feature_info", "kind": 2, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "def get_feature_info() -> Dict[str, str]:\n    \"\"\"\n    获取当前使用的特征提取方法信息\n    Returns:\n        特征提取方法信息字典\n    \"\"\"\n    return {\n        \"protein_feature\": \"protein2vector/attention_fusion/seq2attention.py (ESM2+MSA attention fusion)\",\n        \"smiles_feature\": \"smiles2vector/smiles_feature.py (MolFormer)\",\n        \"prediction_model\": \"kcat_part/predict.py (最佳预训练Transformer模型)\",", "detail": "kcat_predict", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "def main():\n    \"\"\"命令行接口\"\"\"\n    import argparse\n    import pandas as pd\n    import sys\n    parser = argparse.ArgumentParser(description='Kcat预测工具')\n    parser.add_argument('-i', '--input', help='输入CSV文件路径')\n    parser.add_argument('-o', '--output', help='输出CSV文件路径')\n    parser.add_argument('--protein', help='单个蛋白质序列')\n    parser.add_argument('--smiles', help='单个化合物SMILES')", "detail": "kcat_predict", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "logger = logging.getLogger(__name__)\n# 抑制警告\nwarnings.filterwarnings('ignore')\n# 添加必要的路径\ncurrent_dir = os.getcwd()\nprotein2vector_path = os.path.join(current_dir, 'protein2vector', 'attention_fusion')\nsmiles2vector_path = os.path.join(current_dir, 'smiles2vector')\nkcat_part_path = os.path.join(current_dir, 'kcat_part')\n# 确保路径存在并添加到sys.path\nfor path in [protein2vector_path, smiles2vector_path, kcat_part_path]:", "detail": "kcat_predict", "documentation": {}}, {"label": "current_dir", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "current_dir = os.getcwd()\nprotein2vector_path = os.path.join(current_dir, 'protein2vector', 'attention_fusion')\nsmiles2vector_path = os.path.join(current_dir, 'smiles2vector')\nkcat_part_path = os.path.join(current_dir, 'kcat_part')\n# 确保路径存在并添加到sys.path\nfor path in [protein2vector_path, smiles2vector_path, kcat_part_path]:\n    if os.path.exists(path) and path not in sys.path:\n        sys.path.insert(0, path)\n# ==================== 严格使用原始核心组件 ====================\ndef _import_seq2attention():", "detail": "kcat_predict", "documentation": {}}, {"label": "protein2vector_path", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "protein2vector_path = os.path.join(current_dir, 'protein2vector', 'attention_fusion')\nsmiles2vector_path = os.path.join(current_dir, 'smiles2vector')\nkcat_part_path = os.path.join(current_dir, 'kcat_part')\n# 确保路径存在并添加到sys.path\nfor path in [protein2vector_path, smiles2vector_path, kcat_part_path]:\n    if os.path.exists(path) and path not in sys.path:\n        sys.path.insert(0, path)\n# ==================== 严格使用原始核心组件 ====================\ndef _import_seq2attention():\n    \"\"\"导入原始的seq2attention函数\"\"\"", "detail": "kcat_predict", "documentation": {}}, {"label": "smiles2vector_path", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "smiles2vector_path = os.path.join(current_dir, 'smiles2vector')\nkcat_part_path = os.path.join(current_dir, 'kcat_part')\n# 确保路径存在并添加到sys.path\nfor path in [protein2vector_path, smiles2vector_path, kcat_part_path]:\n    if os.path.exists(path) and path not in sys.path:\n        sys.path.insert(0, path)\n# ==================== 严格使用原始核心组件 ====================\ndef _import_seq2attention():\n    \"\"\"导入原始的seq2attention函数\"\"\"\n    try:", "detail": "kcat_predict", "documentation": {}}, {"label": "kcat_part_path", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "kcat_part_path = os.path.join(current_dir, 'kcat_part')\n# 确保路径存在并添加到sys.path\nfor path in [protein2vector_path, smiles2vector_path, kcat_part_path]:\n    if os.path.exists(path) and path not in sys.path:\n        sys.path.insert(0, path)\n# ==================== 严格使用原始核心组件 ====================\ndef _import_seq2attention():\n    \"\"\"导入原始的seq2attention函数\"\"\"\n    try:\n        # 确保protein2vector路径在sys.path中", "detail": "kcat_predict", "documentation": {}}, {"label": "_global_strict_predictor", "kind": 5, "importPath": "kcat_predict", "description": "kcat_predict", "peekOfCode": "_global_strict_predictor = None\ndef _get_strict_predictor():\n    \"\"\"获取全局严格预测器实例\"\"\"\n    global _global_strict_predictor\n    if _global_strict_predictor is None:\n        _global_strict_predictor = StrictKcatPredictorComplete()\n    return _global_strict_predictor\n# ==================== 严格模式公共API ====================\ndef predict_kcat(protein_sequence: str, smiles: str, verbose: bool = False) -> float:\n    \"\"\"", "detail": "kcat_predict", "documentation": {}}]