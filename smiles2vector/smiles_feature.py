import pandas as pd
import numpy as np
import torch
from transformers import AutoTokenizer, AutoModel
from tqdm import tqdm
import os

# 1. Device
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 2. Load MolFormer
molformer_path = "/usr/XML/database/MolFormer"
mol_tokenizer = AutoTokenizer.from_pretrained(molformer_path, trust_remote_code=True)
mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True, deterministic_eval=True).to(device)
# mol_model = AutoModel.from_pretrained(molformer_path, trust_remote_code=True).to(device)
mol_model.eval()

# 3. Molecule feature extraction
def extract_molformer_features(smiles):
    try:
        inputs = mol_tokenizer(smiles, return_tensors="pt", padding="max_length", truncation=True).to(device)
        with torch.no_grad():
            output = mol_model(**inputs)
        # vector = output.last_hidden_state[:, 0, :].squeeze(0).cpu().numpy()
        vector= output.pooler_output.cpu().numpy()
        # print(vector[0][:2])
        return vector, ""
    except Exception as e:
        return None, str(e)

# 4. Main processing
def extract_and_save_molformer_features(input_path, output_path):
    df = pd.read_excel(input_path)
    print(f"Loaded {len(df)} samples from {input_path}")

    output_rows = []

    for _, row in tqdm(df.iterrows(), total=len(df)):
        sample_id = row["ID"]
        seq = row.get("Sequence", "")
        smiles = row["Smiles"]
        # km_value = row["Log10_Km_Value"]

        vector, error = extract_molformer_features(smiles)
        success = vector is not None

        if success:
            vector_flat = vector.flatten()
            fusion_vector = ",".join(map(str, vector_flat.tolist()))
            vector_dim = len(vector_flat)
        else:
            fusion_vector = ""
            vector_dim = 0

        output_rows.append({
            "ID": sample_id,
            "Sequence": seq,
            "Smiles": smiles,
            # "Log10_Kcat_Value": kcat_value,
            "success": str(success).upper(),
            "fusion_vector": fusion_vector,
            "vector_dim": vector_dim,
            "error": error
        })

    out_df = pd.DataFrame(output_rows)
    out_df.to_csv(output_path, sep="\t", index=False)
    print(f"Saved extracted features to: {output_path}")

# 5. Run
if __name__ == "__main__":
    extract_and_save_molformer_features(
        input_path="../Data/cluster_0.8.xlsx",
        output_path="../Data/molformer_features.tsv"
    )

