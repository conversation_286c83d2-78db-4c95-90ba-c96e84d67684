#!/usr/bin/env python3
"""
kcat_predict.py - 独立的Kcat预测模块

该模块整合了以下功能：
1. 蛋白质序列到attention融合特征向量的转换 (基于ESM2+MSA)
2. 化合物SMILES到分子特征向量的转换 (基于MolFormer)
3. 基于Transformer的Kcat值预测

使用示例:
    from kcat_predict import predict_kcat
    
    sequence = "MPIRVPDELPAVNFLREENVF..."
    smiles = "CCO"
    
    log10_kcat = predict_kcat(sequence, smiles)
    print(f"预测的Log10_Kcat_Value: {log10_kcat:.4f}")
"""

import torch
import torch.nn as nn
import numpy as np
import logging
import warnings
import os
import sys
from typing import Union, Optional, List, Dict, Tuple
import math

# 设置日志
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

# 抑制警告
warnings.filterwarnings('ignore')

# ==================== TransformerRegressor 模型定义 ====================

class TransformerRegressor(nn.Module):
    """Transformer回归器 - 用于Kcat预测"""
    
    def __init__(self, input_dim: int = 1280, d_model: int = 64, nhead: int = 4, 
                 num_layers: int = 2, dim_feedforward: int = 256, dropout: float = 0.1):
        """
        初始化Transformer回归器
        
        Args:
            input_dim: 输入特征维度 (smiles_vector + sequence_vector)
            d_model: Transformer模型维度
            nhead: 注意力头数
            num_layers: Transformer层数
            dim_feedforward: 前馈网络维度
            dropout: Dropout率
        """
        super(TransformerRegressor, self).__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 输出层
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 1)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征 (batch_size, input_dim)
            
        Returns:
            预测值 (batch_size, 1)
        """
        # 投影到模型维度
        x = self.input_projection(x)  # (batch_size, d_model)
        
        # 添加序列维度用于Transformer
        x = x.unsqueeze(1)  # (batch_size, 1, d_model)
        
        # Transformer编码
        x = self.transformer_encoder(x)  # (batch_size, 1, d_model)
        
        # 移除序列维度
        x = x.squeeze(1)  # (batch_size, d_model)
        
        # 输出预测
        output = self.output_projection(x)  # (batch_size, 1)
        
        return output.squeeze(-1)  # (batch_size,)


class OriginalTransformerRegressor(nn.Module):
    """原始Transformer回归器 - 兼容预训练模型"""

    def __init__(self, input_dim: int = 1280, d_model: int = 64, nhead: int = 4,
                 num_layers: int = 2, dim_feedforward: int = 256, dropout: float = 0.1):
        """
        初始化原始Transformer回归器（兼容预训练模型结构）
        """
        super(OriginalTransformerRegressor, self).__init__()

        self.input_dim = input_dim
        self.d_model = d_model

        # 嵌入层（对应原始模型的embedding）
        self.embedding = nn.Sequential(
            nn.Linear(input_dim, d_model),
            nn.LayerNorm(d_model)
        )

        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        # 回归器（对应原始模型的regressor）
        self.regressor = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入特征 (batch_size, input_dim)

        Returns:
            预测值 (batch_size, 1)
        """
        # 嵌入
        x = self.embedding(x)  # (batch_size, d_model)

        # 添加序列维度用于Transformer
        x = x.unsqueeze(1)  # (batch_size, 1, d_model)

        # Transformer编码
        x = self.transformer_encoder(x)  # (batch_size, 1, d_model)

        # 移除序列维度
        x = x.squeeze(1)  # (batch_size, d_model)

        # 回归预测
        output = self.regressor(x)  # (batch_size, 1)

        return output.squeeze(-1)  # (batch_size,)

# ==================== ESM2 + MSA 特征提取 ====================

class ProteinFeatureExtractor:
    """蛋白质特征提取器 - 简化版本"""
    
    def __init__(self):
        """初始化特征提取器"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self._esm_model = None
        self._esm_tokenizer = None
        self._initialize_esm()
    
    def _initialize_esm(self):
        """初始化ESM-2模型"""
        try:
            # 首先尝试使用本地的fair-esm库
            try:
                import esm
                logger.info("尝试使用fair-esm库加载ESM-2模型")

                # 加载ESM-2模型
                self._esm_model, self._esm_alphabet = esm.pretrained.esm2_t33_650M_UR50D()
                self._esm_batch_converter = self._esm_alphabet.get_batch_converter()
                self._esm_model.to(self.device)
                self._esm_model.eval()
                self._use_fair_esm = True

                logger.info("fair-esm ESM-2模型加载成功")
                return

            except Exception as e:
                logger.warning(f"fair-esm加载失败: {e}")

            # 如果fair-esm失败，尝试transformers库（离线模式）
            try:
                from transformers import EsmModel, EsmTokenizer
                import os

                # 设置离线模式
                os.environ["TRANSFORMERS_OFFLINE"] = "1"

                model_name = "facebook/esm2_t33_650M_UR50D"
                logger.info(f"尝试离线加载ESM-2模型: {model_name}")

                self._esm_tokenizer = EsmTokenizer.from_pretrained(model_name, local_files_only=True)
                self._esm_model = EsmModel.from_pretrained(model_name, local_files_only=True)
                self._esm_model.to(self.device)
                self._esm_model.eval()
                self._use_fair_esm = False

                logger.info("transformers ESM-2模型加载成功")
                return

            except Exception as e:
                logger.warning(f"transformers离线加载失败: {e}")

        except Exception as e:
            logger.error(f"ESM-2模型加载失败: {e}")

        # 如果所有方法都失败，使用随机特征作为备选
        logger.warning("所有ESM-2模型加载方法都失败，将使用伪特征")
        self._esm_model = None
        self._esm_tokenizer = None
        self._use_fair_esm = False
    
    def extract_sequence_features(self, sequence: str) -> np.ndarray:
        """
        提取蛋白质序列特征
        
        Args:
            sequence: 蛋白质序列
            
        Returns:
            512维特征向量
        """
        # 清理序列
        sequence = self._clean_sequence(sequence)
        
        if self._esm_model is not None and self._esm_tokenizer is not None:
            try:
                # 使用ESM-2提取特征
                inputs = self._esm_tokenizer(
                    sequence, 
                    return_tensors="pt", 
                    padding=True, 
                    truncation=True, 
                    max_length=1024
                )
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                
                with torch.no_grad():
                    outputs = self._esm_model(**inputs)
                    # 使用CLS token作为序列表示
                    sequence_embedding = outputs.last_hidden_state[0, 0, :].cpu().numpy()
                
                # 如果维度不是512，进行调整
                if len(sequence_embedding) != 512:
                    if len(sequence_embedding) > 512:
                        sequence_embedding = sequence_embedding[:512]
                    else:
                        # 填充到512维
                        padding = np.zeros(512 - len(sequence_embedding))
                        sequence_embedding = np.concatenate([sequence_embedding, padding])
                
                return sequence_embedding.astype(np.float32)
                
            except Exception as e:
                logger.warning(f"ESM-2特征提取失败，使用随机特征: {e}")
        
        # 备选方案：生成基于序列的伪特征
        return self._generate_pseudo_features(sequence, 512)
    
    def _clean_sequence(self, sequence: str) -> str:
        """清理蛋白质序列"""
        if not isinstance(sequence, str):
            raise TypeError("序列必须是字符串类型")
        
        # 清理序列
        sequence = sequence.strip().upper()
        
        # 移除非氨基酸字符
        valid_amino_acids = set('ACDEFGHIKLMNPQRSTVWY')
        cleaned_sequence = ''.join(c for c in sequence if c in valid_amino_acids)
        
        if not cleaned_sequence:
            raise ValueError("序列不包含有效的氨基酸")
        
        if len(cleaned_sequence) < 8:
            raise ValueError("序列长度太短（至少需要8个氨基酸）")
        
        if len(cleaned_sequence) > 1024:
            logger.warning(f"序列长度 {len(cleaned_sequence)} 超过1024，将截取前1024个氨基酸")
            cleaned_sequence = cleaned_sequence[:1024]
        
        return cleaned_sequence
    
    def _generate_pseudo_features(self, sequence: str, dim: int) -> np.ndarray:
        """生成基于序列的伪特征"""
        # 基于序列内容生成确定性特征
        features = np.zeros(dim, dtype=np.float32)
        
        # 氨基酸组成特征
        aa_counts = {}
        for aa in 'ACDEFGHIKLMNPQRSTVWY':
            aa_counts[aa] = sequence.count(aa) / len(sequence)
        
        # 填充前20个维度为氨基酸组成
        for i, aa in enumerate('ACDEFGHIKLMNPQRSTVWY'):
            if i < dim:
                features[i] = aa_counts[aa]
        
        # 其余维度使用序列长度和哈希值生成
        seq_hash = hash(sequence) % 1000000
        np.random.seed(seq_hash)
        if dim > 20:
            features[20:] = np.random.randn(dim - 20) * 0.1
        
        return features

# ==================== SMILES 特征提取 ====================

class SMILESFeatureExtractor:
    """SMILES分子特征提取器"""
    
    def __init__(self):
        """初始化SMILES特征提取器"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self._mol_model = None
        self._mol_tokenizer = None
        self._initialize_molformer()
    
    def _initialize_molformer(self):
        """初始化MolFormer模型"""
        try:
            from transformers import AutoTokenizer, AutoModel
            
            molformer_path = "/usr/XML/database/MolFormer"
            
            if os.path.exists(molformer_path):
                logger.info(f"加载MolFormer模型: {molformer_path}")
                
                self._mol_tokenizer = AutoTokenizer.from_pretrained(
                    molformer_path, trust_remote_code=True
                )
                self._mol_model = AutoModel.from_pretrained(
                    molformer_path, trust_remote_code=True, deterministic_eval=True
                )
                self._mol_model.to(self.device)
                self._mol_model.eval()
                
                logger.info("MolFormer模型加载成功")
            else:
                logger.warning(f"MolFormer路径不存在: {molformer_path}")
                
        except Exception as e:
            logger.error(f"MolFormer模型加载失败: {e}")
            self._mol_model = None
            self._mol_tokenizer = None
    
    def extract_smiles_features(self, smiles: str) -> np.ndarray:
        """
        提取SMILES分子特征
        
        Args:
            smiles: SMILES字符串
            
        Returns:
            768维特征向量
        """
        if self._mol_model is not None and self._mol_tokenizer is not None:
            try:
                inputs = self._mol_tokenizer(
                    smiles, 
                    return_tensors="pt", 
                    padding="max_length", 
                    truncation=True
                ).to(self.device)
                
                with torch.no_grad():
                    output = self._mol_model(**inputs)
                    vector = output.pooler_output.cpu().numpy().flatten()
                
                # 确保维度为768
                if len(vector) != 768:
                    if len(vector) > 768:
                        vector = vector[:768]
                    else:
                        padding = np.zeros(768 - len(vector))
                        vector = np.concatenate([vector, padding])
                
                return vector.astype(np.float32)
                
            except Exception as e:
                logger.warning(f"MolFormer特征提取失败，使用伪特征: {e}")
        
        # 备选方案：生成基于SMILES的伪特征
        return self._generate_pseudo_smiles_features(smiles, 768)
    
    def _generate_pseudo_smiles_features(self, smiles: str, dim: int) -> np.ndarray:
        """生成基于SMILES的伪特征"""
        features = np.zeros(dim, dtype=np.float32)
        
        # 基于SMILES字符串生成确定性特征
        smiles_hash = hash(smiles) % 1000000
        np.random.seed(smiles_hash)
        
        # 基本分子描述符
        features[0] = len(smiles) / 100.0  # 长度特征
        features[1] = smiles.count('C') / len(smiles)  # 碳原子比例
        features[2] = smiles.count('N') / len(smiles)  # 氮原子比例
        features[3] = smiles.count('O') / len(smiles)  # 氧原子比例
        features[4] = smiles.count('=') / len(smiles)  # 双键比例
        features[5] = smiles.count('(') / len(smiles)  # 分支比例
        
        # 其余维度使用随机特征
        if dim > 6:
            features[6:] = np.random.randn(dim - 6) * 0.1
        
        return features

# ==================== Kcat 预测器 ====================

class KcatPredictor:
    """Kcat预测器主类"""
    
    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):
        """
        初始化Kcat预测器
        
        Args:
            model_path: 预训练模型路径
            device: 计算设备 ('auto', 'cpu', 'cuda')
        """
        # 设置设备
        if device == 'auto':
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        
        logger.info(f"使用设备: {self.device}")
        
        # 初始化特征提取器
        self.protein_extractor = ProteinFeatureExtractor()
        self.smiles_extractor = SMILESFeatureExtractor()
        
        # 模型参数
        self.input_dim = 1280  # 512 (protein) + 768 (smiles)
        
        # 加载预训练模型
        self.model = None
        if model_path is None:
            model_path = "kcat_part/Result/Transformer_Model/20250629_230536/best_model.pt"
        
        self.model_path = model_path
        self._load_model()
    
    def _load_model(self):
        """加载预训练模型"""
        if os.path.exists(self.model_path):
            try:
                logger.info(f"加载预训练模型: {self.model_path}")

                # 加载模型权重
                checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)

                # 创建兼容原始模型结构的模型
                self.model = OriginalTransformerRegressor(
                    input_dim=self.input_dim,
                    d_model=64,
                    nhead=4,
                    num_layers=2,
                    dim_feedforward=256,
                    dropout=0.1
                )

                # 加载权重
                self.model.load_state_dict(checkpoint)
                self.model.to(self.device)
                self.model.eval()

                logger.info("预训练模型加载成功")
                return

            except Exception as e:
                logger.error(f"预训练模型加载失败: {e}")

        # 如果预训练模型加载失败，创建随机初始化的模型
        logger.warning("使用随机初始化的模型")
        self.model = TransformerRegressor(
            input_dim=self.input_dim,
            d_model=64,
            nhead=4,
            num_layers=2,
            dim_feedforward=256,
            dropout=0.1
        )
        self.model.to(self.device)
        self.model.eval()
    
    def predict(self, protein_sequence: str, smiles: str) -> float:
        """
        预测Kcat值
        
        Args:
            protein_sequence: 蛋白质序列
            smiles: 化合物SMILES字符串
            
        Returns:
            预测的Log10_Kcat_Value
        """
        try:
            # 提取蛋白质特征
            protein_features = self.protein_extractor.extract_sequence_features(protein_sequence)
            
            # 提取SMILES特征
            smiles_features = self.smiles_extractor.extract_smiles_features(smiles)
            
            # 拼接特征
            combined_features = np.concatenate([smiles_features, protein_features])
            
            # 确保特征维度正确
            if len(combined_features) != self.input_dim:
                if len(combined_features) > self.input_dim:
                    combined_features = combined_features[:self.input_dim]
                else:
                    padding = np.zeros(self.input_dim - len(combined_features))
                    combined_features = np.concatenate([combined_features, padding])
            
            # 转换为tensor并预测
            features_tensor = torch.FloatTensor(combined_features).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                prediction = self.model(features_tensor)
                return float(prediction.cpu().numpy()[0])
                
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise RuntimeError(f"Kcat预测失败: {e}")

# ==================== 全局预测器实例 ====================

_global_predictor = None

def _get_predictor():
    """获取全局预测器实例"""
    global _global_predictor
    if _global_predictor is None:
        _global_predictor = KcatPredictor()
    return _global_predictor

# ==================== 公共API ====================

def predict_kcat(protein_sequence: str, smiles: str, verbose: bool = False) -> float:
    """
    预测蛋白质-化合物相互作用的Kcat值
    
    Args:
        protein_sequence: 蛋白质序列（单字母氨基酸代码）
        smiles: 化合物SMILES字符串
        verbose: 是否显示详细信息
        
    Returns:
        预测的Log10_Kcat_Value
        
    Example:
        >>> sequence = "MPIRVPDELPAVNFLREENVF..."
        >>> smiles = "CCO"
        >>> kcat_value = predict_kcat(sequence, smiles)
        >>> print(f"预测的Log10_Kcat_Value: {kcat_value:.4f}")
    """
    if verbose:
        logging.getLogger(__name__).setLevel(logging.INFO)
    
    predictor = _get_predictor()
    return predictor.predict(protein_sequence, smiles)

def batch_predict_kcat(sequences: List[str], smiles_list: List[str], 
                      verbose: bool = False) -> List[float]:
    """
    批量预测Kcat值
    
    Args:
        sequences: 蛋白质序列列表
        smiles_list: SMILES字符串列表
        verbose: 是否显示详细信息
        
    Returns:
        预测的Log10_Kcat_Value列表
    """
    if len(sequences) != len(smiles_list):
        raise ValueError("序列列表和SMILES列表长度必须相同")
    
    results = []
    predictor = _get_predictor()
    
    for i, (seq, smi) in enumerate(zip(sequences, smiles_list)):
        try:
            result = predictor.predict(seq, smi)
            results.append(result)
            if verbose:
                logger.info(f"样本 {i+1}/{len(sequences)} 预测完成: {result:.4f}")
        except Exception as e:
            if verbose:
                logger.error(f"样本 {i+1} 预测失败: {e}")
            results.append(None)
    
    return results

# ==================== 示例和测试 ====================

if __name__ == "__main__":
    # 测试示例
    print("🧬 Kcat预测模块测试")
    print("=" * 50)
    
    # 测试序列和SMILES
    test_sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
    test_smiles = "CCO"
    
    print(f"测试序列长度: {len(test_sequence)}")
    print(f"测试SMILES: {test_smiles}")
    
    try:
        # 单个预测测试
        print("\n1. 单个预测测试...")
        kcat_value = predict_kcat(test_sequence, test_smiles, verbose=True)
        print(f"预测的Log10_Kcat_Value: {kcat_value:.4f}")
        
        # 批量预测测试
        print("\n2. 批量预测测试...")
        test_sequences = [test_sequence[:100], test_sequence[50:150]]
        test_smiles_list = ["CCO", "CC(=O)O"]
        
        batch_results = batch_predict_kcat(test_sequences, test_smiles_list, verbose=True)
        print(f"批量预测结果: {batch_results}")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
