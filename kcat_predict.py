#!/usr/bin/env python3
"""
kcat_predict.py - 严格基于原始核心组件的Kcat预测模块

该模块严格使用以下原始组件，不做任何降级或修改：
1. protein2vector/attention_fusion/seq2attention.py - ESM2+MSA attention fusion特征提取
2. smiles2vector/smiles_feature.py - MolFormer化合物特征提取
3. kcat_part/predict.py - 最佳预训练Transformer模型预测

使用示例:
    from kcat_predict import predict_kcat

    sequence = "MPIRVPDELPAVNFLREENVF..."
    smiles = "CCO"

    log10_kcat = predict_kcat(sequence, smiles)
    print(f"预测的Log10_Kcat_Value: {log10_kcat:.4f}")
"""

import torch
import torch.nn as nn
import numpy as np
import logging
import warnings
import os
import sys
from typing import Union, Optional, List, Dict, Tuple
import math

# 设置日志
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

# 抑制警告
warnings.filterwarnings('ignore')

# 添加必要的路径
current_dir = os.getcwd()
protein2vector_path = os.path.join(current_dir, 'protein2vector', 'attention_fusion')
smiles2vector_path = os.path.join(current_dir, 'smiles2vector')
kcat_part_path = os.path.join(current_dir, 'kcat_part')

# 确保路径存在并添加到sys.path
for path in [protein2vector_path, smiles2vector_path, kcat_part_path]:
    if os.path.exists(path) and path not in sys.path:
        sys.path.insert(0, path)

# ==================== 严格使用原始核心组件 ====================

def _import_seq2attention():
    """导入原始的seq2attention函数"""
    try:
        # 确保protein2vector路径在sys.path中
        protein2vector_path = os.path.join(os.getcwd(), 'protein2vector', 'attention_fusion')
        if protein2vector_path not in sys.path:
            sys.path.insert(0, protein2vector_path)

        from seq2attention import seq2attention
        return seq2attention
    except ImportError as e:
        raise ImportError(f"无法导入seq2attention: {e}. 请确保protein2vector/attention_fusion/seq2attention.py存在且可访问")

def _import_smiles_feature():
    """导入原始的SMILES特征提取函数"""
    try:
        # 确保smiles2vector路径在sys.path中
        smiles2vector_path = os.path.join(os.getcwd(), 'smiles2vector')
        if smiles2vector_path not in sys.path:
            sys.path.insert(0, smiles2vector_path)

        from smiles_feature import extract_molformer_features
        return extract_molformer_features
    except ImportError as e:
        raise ImportError(f"无法导入extract_molformer_features: {e}. 请确保smiles2vector/smiles_feature.py存在且可访问")

def _create_original_transformer_regressor():
    """创建与原始模型完全兼容的TransformerRegressor类"""

    class OriginalTransformerRegressor(nn.Module):
        """与预训练模型完全兼容的TransformerRegressor"""

        def __init__(self, input_dim=1280, d_model=64, nhead=4, num_layers=2,
                     dim_feedforward=256, dropout=0.1):
            super(OriginalTransformerRegressor, self).__init__()

            # 嵌入层 (对应模型中的embedding)
            self.embedding = nn.Sequential(
                nn.Linear(input_dim, d_model),
                nn.LayerNorm(d_model)
            )

            # Transformer编码器
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=d_model,
                nhead=nhead,
                dim_feedforward=dim_feedforward,
                dropout=dropout,
                batch_first=True
            )
            self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

            # 回归器 (对应模型中的regressor)
            self.regressor = nn.Sequential(
                nn.Linear(d_model, d_model // 2),  # 64 -> 32
                nn.ReLU(),
                nn.Linear(d_model // 2, 1)         # 32 -> 1
            )

        def forward(self, x):
            # 嵌入
            x = self.embedding(x)  # (batch_size, d_model)

            # 添加序列维度
            x = x.unsqueeze(1)  # (batch_size, 1, d_model)

            # Transformer编码
            x = self.transformer_encoder(x)  # (batch_size, 1, d_model)

            # 移除序列维度
            x = x.squeeze(1)  # (batch_size, d_model)

            # 回归预测
            output = self.regressor(x)  # (batch_size, 1)

            return output.squeeze(-1)  # (batch_size,)

    return OriginalTransformerRegressor

def _create_original_kcat_predictor():
    """创建与原始predict.py兼容的KcatPredictor类"""

    TransformerRegressor = _create_original_transformer_regressor()

    class OriginalKcatPredictor:
        """与原始predict.py完全兼容的KcatPredictor"""

        def __init__(self, model_path=None, device='auto'):
            # 设置设备
            if device == 'auto':
                self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            else:
                self.device = torch.device(device)

            # 模型参数
            self.input_dim = 1280  # 768 (smiles) + 512 (protein)

            # 设置模型路径
            if model_path is None:
                model_path = "Result/Transformer_Model/20250629_230536/best_model.pt"

            # 转换为绝对路径
            if not os.path.isabs(model_path):
                model_path = os.path.join(os.getcwd(), 'kcat_part', model_path)

            self.model_path = model_path
            self.model = None
            self._load_model()

        def _load_model(self):
            """加载预训练模型"""
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

            logger.info(f"加载模型: {self.model_path}")

            try:
                # 加载模型权重
                checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)

                # 创建模型 (严格按照原始参数)
                self.model = TransformerRegressor(
                    input_dim=self.input_dim,
                    d_model=64,
                    nhead=4,
                    num_layers=2,
                    dim_feedforward=256,
                    dropout=0.1
                )

                # 加载权重
                self.model.load_state_dict(checkpoint)
                self.model.to(self.device)
                self.model.eval()

                logger.info("模型加载成功")

            except Exception as e:
                raise RuntimeError(f"模型加载失败: {e}")

        def predict_single(self, smiles_vector, sequence_vector):
            """单样本预测"""
            if self.model is None:
                raise RuntimeError("模型未加载")

            # 确保输入是numpy数组
            if not isinstance(smiles_vector, np.ndarray):
                smiles_vector = np.array(smiles_vector)
            if not isinstance(sequence_vector, np.ndarray):
                sequence_vector = np.array(sequence_vector)

            # 拼接特征 (smiles + sequence)
            combined_features = np.concatenate([smiles_vector.flatten(), sequence_vector.flatten()])

            # 确保特征维度正确
            if len(combined_features) != self.input_dim:
                if len(combined_features) > self.input_dim:
                    combined_features = combined_features[:self.input_dim]
                else:
                    padding = np.zeros(self.input_dim - len(combined_features))
                    combined_features = np.concatenate([combined_features, padding])

            # 转换为tensor
            features_tensor = torch.FloatTensor(combined_features).unsqueeze(0).to(self.device)

            # 预测
            with torch.no_grad():
                prediction = self.model(features_tensor)
                return float(prediction.cpu().numpy()[0])

        def predict_batch(self, smiles_vectors, sequence_vectors, batch_size=64):
            """批量预测"""
            if self.model is None:
                raise RuntimeError("模型未加载")

            results = []

            for i in range(0, len(smiles_vectors), batch_size):
                batch_smiles = smiles_vectors[i:i+batch_size]
                batch_sequences = sequence_vectors[i:i+batch_size]

                # 准备批量特征
                batch_features = []
                for smi_vec, seq_vec in zip(batch_smiles, batch_sequences):
                    # 确保是numpy数组
                    if not isinstance(smi_vec, np.ndarray):
                        smi_vec = np.array(smi_vec)
                    if not isinstance(seq_vec, np.ndarray):
                        seq_vec = np.array(seq_vec)

                    # 拼接特征
                    combined = np.concatenate([smi_vec.flatten(), seq_vec.flatten()])

                    # 确保维度正确
                    if len(combined) != self.input_dim:
                        if len(combined) > self.input_dim:
                            combined = combined[:self.input_dim]
                        else:
                            padding = np.zeros(self.input_dim - len(combined))
                            combined = np.concatenate([combined, padding])

                    batch_features.append(combined)

                # 转换为tensor
                batch_tensor = torch.FloatTensor(batch_features).to(self.device)

                # 批量预测
                with torch.no_grad():
                    predictions = self.model(batch_tensor)
                    batch_results = predictions.cpu().numpy().tolist()
                    results.extend(batch_results)

            return results

    return OriginalKcatPredictor

# ==================== 严格的原始组件封装器 ====================

class StrictProteinFeatureExtractor:
    """严格使用原始seq2attention的蛋白质特征提取器"""

    def __init__(self):
        """初始化特征提取器"""
        self.seq2attention = _import_seq2attention()
        logger.info("成功导入原始seq2attention函数")

    def extract_sequence_features(self, sequence: str) -> np.ndarray:
        """
        严格使用原始seq2attention提取蛋白质特征

        Args:
            sequence: 蛋白质序列

        Returns:
            512维attention fusion特征向量
        """
        try:
            # 严格使用原始的seq2attention函数，不做任何修改
            feature_vector = self.seq2attention(sequence, return_numpy=True, verbose=False)

            if feature_vector is None:
                raise RuntimeError("seq2attention返回了None")

            if not isinstance(feature_vector, np.ndarray):
                raise RuntimeError(f"seq2attention返回了错误的类型: {type(feature_vector)}")

            if feature_vector.shape != (512,):
                raise RuntimeError(f"seq2attention返回了错误的维度: {feature_vector.shape}, 期望: (512,)")

            logger.info(f"成功使用原始seq2attention提取特征，维度: {feature_vector.shape}")
            return feature_vector.astype(np.float32)

        except Exception as e:
            logger.error(f"原始seq2attention特征提取失败: {e}")
            raise RuntimeError(f"严格模式下不允许降级，seq2attention必须成功: {e}")

class StrictSMILESFeatureExtractor:
    """严格使用原始smiles_feature的SMILES特征提取器"""

    def __init__(self):
        """初始化SMILES特征提取器"""
        self.extract_molformer_features = _import_smiles_feature()
        logger.info("成功导入原始extract_molformer_features函数")

    def extract_smiles_features(self, smiles: str) -> np.ndarray:
        """
        严格使用原始extract_molformer_features提取SMILES特征

        Args:
            smiles: SMILES字符串

        Returns:
            768维MolFormer特征向量
        """
        try:
            # 严格使用原始的extract_molformer_features函数
            vector, error = self.extract_molformer_features(smiles)

            if vector is None:
                raise RuntimeError(f"extract_molformer_features失败: {error}")

            # 确保是numpy数组
            if not isinstance(vector, np.ndarray):
                vector = np.array(vector)

            # 展平向量
            vector_flat = vector.flatten()

            # 验证维度（MolFormer通常输出768维）
            if len(vector_flat) != 768:
                raise RuntimeError(f"MolFormer返回了错误的维度: {len(vector_flat)}, 期望: 768")

            logger.info(f"成功使用原始extract_molformer_features提取特征，维度: {vector_flat.shape}")
            return vector_flat.astype(np.float32)

        except Exception as e:
            logger.error(f"原始extract_molformer_features特征提取失败: {e}")
            raise RuntimeError(f"严格模式下不允许降级，extract_molformer_features必须成功: {e}")

class StrictKcatPredictor:
    """严格使用原始predict.py的Kcat预测器"""

    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):
        """
        初始化严格的Kcat预测器

        Args:
            model_path: 预训练模型路径
            device: 计算设备
        """
        try:
            # 导入原始的KcatPredictor
            OriginalKcatPredictor = _import_original_predictor()

            # 使用原始的KcatPredictor，严格保持原始参数
            if model_path is None:
                # 使用原始的最佳模型路径
                model_path = "Result/Transformer_Model/20250629_230536/best_model.pt"

            self.original_predictor = OriginalKcatPredictor(model_path=model_path, device=device)
            logger.info("成功初始化原始KcatPredictor")

        except Exception as e:
            logger.error(f"原始KcatPredictor初始化失败: {e}")
            raise RuntimeError(f"严格模式下不允许降级，原始KcatPredictor必须成功初始化: {e}")

    def predict_single(self, smiles_vector: np.ndarray, sequence_vector: np.ndarray) -> float:
        """
        严格使用原始predict_single方法

        Args:
            smiles_vector: SMILES特征向量
            sequence_vector: 蛋白质序列特征向量

        Returns:
            预测的Log10_Kcat_Value
        """
        try:
            # 严格使用原始的predict_single方法
            result = self.original_predictor.predict_single(smiles_vector, sequence_vector)

            if not isinstance(result, (int, float)):
                raise RuntimeError(f"原始predict_single返回了错误的类型: {type(result)}")

            logger.info(f"成功使用原始predict_single进行预测: {result}")
            return float(result)

        except Exception as e:
            logger.error(f"原始predict_single预测失败: {e}")
            raise RuntimeError(f"严格模式下不允许降级，原始predict_single必须成功: {e}")

    def predict_batch(self, smiles_vectors: List[np.ndarray], sequence_vectors: List[np.ndarray],
                     batch_size: int = 64) -> List[float]:
        """
        严格使用原始predict_batch方法

        Args:
            smiles_vectors: SMILES特征向量列表
            sequence_vectors: 蛋白质序列特征向量列表
            batch_size: 批次大小

        Returns:
            预测的Log10_Kcat_Value列表
        """
        try:
            # 严格使用原始的predict_batch方法
            results = self.original_predictor.predict_batch(
                smiles_vectors, sequence_vectors, batch_size=batch_size
            )

            if not isinstance(results, list):
                raise RuntimeError(f"原始predict_batch返回了错误的类型: {type(results)}")

            logger.info(f"成功使用原始predict_batch进行批量预测，结果数量: {len(results)}")
            return results

        except Exception as e:
            logger.error(f"原始predict_batch预测失败: {e}")
            raise RuntimeError(f"严格模式下不允许降级，原始predict_batch必须成功: {e}")
    
# ==================== 严格的完整预测器 ====================

class StrictKcatPredictorComplete:
    """严格使用所有原始组件的完整Kcat预测器"""

    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):
        """
        初始化严格的完整Kcat预测器

        Args:
            model_path: 预训练模型路径
            device: 计算设备
        """
        logger.info("初始化严格模式的Kcat预测器...")

        # 初始化严格的特征提取器
        self.protein_extractor = StrictProteinFeatureExtractor()
        self.smiles_extractor = StrictSMILESFeatureExtractor()

        # 初始化严格的预测器
        self.predictor = StrictKcatPredictor(model_path=model_path, device=device)

        logger.info("严格模式的Kcat预测器初始化完成")

    def predict(self, protein_sequence: str, smiles: str) -> float:
        """
        严格使用原始组件进行预测

        Args:
            protein_sequence: 蛋白质序列
            smiles: 化合物SMILES字符串

        Returns:
            预测的Log10_Kcat_Value
        """
        try:
            logger.info("开始严格模式预测...")

            # 严格使用原始seq2attention提取蛋白质特征
            logger.info("使用原始seq2attention提取蛋白质特征...")
            sequence_vector = self.protein_extractor.extract_sequence_features(protein_sequence)

            # 严格使用原始extract_molformer_features提取SMILES特征
            logger.info("使用原始extract_molformer_features提取SMILES特征...")
            smiles_vector = self.smiles_extractor.extract_smiles_features(smiles)

            # 严格使用原始predict_single进行预测
            logger.info("使用原始predict_single进行预测...")
            result = self.predictor.predict_single(smiles_vector, sequence_vector)

            logger.info(f"严格模式预测完成，结果: {result}")
            return result

        except Exception as e:
            logger.error(f"严格模式预测失败: {e}")
            raise RuntimeError(f"严格模式下预测失败，不允许降级: {e}")

    def predict_batch(self, protein_sequences: List[str], smiles_list: List[str],
                     batch_size: int = 64) -> List[float]:
        """
        严格使用原始组件进行批量预测

        Args:
            protein_sequences: 蛋白质序列列表
            smiles_list: SMILES字符串列表
            batch_size: 批次大小

        Returns:
            预测的Log10_Kcat_Value列表
        """
        try:
            if len(protein_sequences) != len(smiles_list):
                raise ValueError("序列列表和SMILES列表长度必须相同")

            logger.info(f"开始严格模式批量预测，样本数: {len(protein_sequences)}")

            # 批量提取特征
            sequence_vectors = []
            smiles_vectors = []

            for i, (seq, smi) in enumerate(zip(protein_sequences, smiles_list)):
                logger.info(f"处理样本 {i+1}/{len(protein_sequences)}")

                # 严格提取特征
                seq_vec = self.protein_extractor.extract_sequence_features(seq)
                smi_vec = self.smiles_extractor.extract_smiles_features(smi)

                sequence_vectors.append(seq_vec)
                smiles_vectors.append(smi_vec)

            # 严格使用原始predict_batch进行预测
            logger.info("使用原始predict_batch进行批量预测...")
            results = self.predictor.predict_batch(smiles_vectors, sequence_vectors, batch_size=batch_size)

            logger.info(f"严格模式批量预测完成，结果数量: {len(results)}")
            return results

        except Exception as e:
            logger.error(f"严格模式批量预测失败: {e}")
            raise RuntimeError(f"严格模式下批量预测失败，不允许降级: {e}")

# ==================== 全局严格预测器实例 ====================

_global_strict_predictor = None

def _get_strict_predictor():
    """获取全局严格预测器实例"""
    global _global_strict_predictor
    if _global_strict_predictor is None:
        _global_strict_predictor = StrictKcatPredictorComplete()
    return _global_strict_predictor

# ==================== 严格模式公共API ====================

def predict_kcat(protein_sequence: str, smiles: str, verbose: bool = False) -> float:
    """
    严格使用原始组件预测蛋白质-化合物相互作用的Kcat值

    该函数严格使用以下原始组件，不做任何降级：
    1. protein2vector/attention_fusion/seq2attention.py - ESM2+MSA attention fusion
    2. smiles2vector/smiles_feature.py - MolFormer特征提取
    3. kcat_part/predict.py - 最佳预训练模型

    Args:
        protein_sequence: 蛋白质序列（单字母氨基酸代码）
        smiles: 化合物SMILES字符串
        verbose: 是否显示详细信息

    Returns:
        预测的Log10_Kcat_Value

    Raises:
        RuntimeError: 当任何原始组件失败时，严格模式不允许降级

    Example:
        >>> sequence = "MPIRVPDELPAVNFLREENVF..."
        >>> smiles = "CCO"
        >>> kcat_value = predict_kcat(sequence, smiles)
        >>> print(f"预测的Log10_Kcat_Value: {kcat_value:.4f}")
    """
    if verbose:
        logging.getLogger(__name__).setLevel(logging.INFO)
        logger.info("启动严格模式预测...")
        logger.info("严格使用: seq2attention + extract_molformer_features + 原始predict_single")

    try:
        predictor = _get_strict_predictor()
        result = predictor.predict(protein_sequence, smiles)

        if verbose:
            logger.info(f"严格模式预测成功: {result:.4f}")

        return result

    except Exception as e:
        if verbose:
            logger.error(f"严格模式预测失败: {e}")
        raise RuntimeError(f"严格模式预测失败，不允许降级: {e}")

def batch_predict_kcat(sequences: List[str], smiles_list: List[str],
                      verbose: bool = False, batch_size: int = 64) -> List[float]:
    """
    严格使用原始组件批量预测Kcat值

    Args:
        sequences: 蛋白质序列列表
        smiles_list: SMILES字符串列表
        verbose: 是否显示详细信息
        batch_size: 批次大小

    Returns:
        预测的Log10_Kcat_Value列表

    Raises:
        RuntimeError: 当任何原始组件失败时，严格模式不允许降级
    """
    if len(sequences) != len(smiles_list):
        raise ValueError("序列列表和SMILES列表长度必须相同")

    if verbose:
        logging.getLogger(__name__).setLevel(logging.INFO)
        logger.info(f"启动严格模式批量预测，样本数: {len(sequences)}")

    try:
        predictor = _get_strict_predictor()
        results = predictor.predict_batch(sequences, smiles_list, batch_size=batch_size)

        if verbose:
            logger.info(f"严格模式批量预测成功，结果数量: {len(results)}")

        return results

    except Exception as e:
        if verbose:
            logger.error(f"严格模式批量预测失败: {e}")
        raise RuntimeError(f"严格模式批量预测失败，不允许降级: {e}")

def get_feature_info() -> Dict[str, str]:
    """
    获取当前使用的特征提取方法信息

    Returns:
        特征提取方法信息字典
    """
    return {
        "protein_feature": "protein2vector/attention_fusion/seq2attention.py (ESM2+MSA attention fusion)",
        "smiles_feature": "smiles2vector/smiles_feature.py (MolFormer)",
        "prediction_model": "kcat_part/predict.py (最佳预训练Transformer模型)",
        "mode": "STRICT - 不允许降级",
        "protein_dim": "512",
        "smiles_dim": "768",
        "total_input_dim": "1280"
    }

# ==================== 严格模式示例和测试 ====================

if __name__ == "__main__":
    # 严格模式测试示例
    print("🧬 严格模式Kcat预测模块测试")
    print("=" * 60)
    print("严格使用原始组件:")
    print("1. protein2vector/attention_fusion/seq2attention.py")
    print("2. smiles2vector/smiles_feature.py")
    print("3. kcat_part/predict.py")
    print("=" * 60)

    # 显示特征信息
    feature_info = get_feature_info()
    print("\n📋 特征提取信息:")
    for key, value in feature_info.items():
        print(f"  {key}: {value}")

    # 测试序列和SMILES
    test_sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
    test_smiles = "CCO"

    print(f"\n🧪 测试数据:")
    print(f"  序列长度: {len(test_sequence)}")
    print(f"  SMILES: {test_smiles}")

    try:
        # 单个预测测试
        print("\n1️⃣ 严格模式单个预测测试...")
        print("   严格使用: seq2attention + extract_molformer_features + predict_single")
        kcat_value = predict_kcat(test_sequence, test_smiles, verbose=True)
        print(f"   ✅ 预测的Log10_Kcat_Value: {kcat_value:.4f}")
        print(f"   ✅ 预测的Kcat值: {10**kcat_value:.2e} s⁻¹")

        # 批量预测测试
        print("\n2️⃣ 严格模式批量预测测试...")
        test_sequences = [test_sequence[:100], test_sequence[50:150]]
        test_smiles_list = ["CCO", "CC(=O)O"]

        batch_results = batch_predict_kcat(test_sequences, test_smiles_list, verbose=True, batch_size=2)
        print(f"   ✅ 批量预测结果:")
        for i, result in enumerate(batch_results):
            print(f"      样本{i+1}: Log10_Kcat = {result:.4f}, Kcat = {10**result:.2e} s⁻¹")

        print("\n🎉 严格模式测试完成！")
        print("✅ 所有原始组件工作正常")
        print("✅ 未发生任何降级")

    except Exception as e:
        print(f"\n❌ 严格模式测试失败: {e}")
        print("⚠️  请检查原始组件是否正确配置:")
        print("   - protein2vector/attention_fusion/seq2attention.py")
        print("   - smiles2vector/smiles_feature.py")
        print("   - kcat_part/predict.py")
        import traceback
        traceback.print_exc()
