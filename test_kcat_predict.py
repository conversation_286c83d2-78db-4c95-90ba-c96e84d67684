#!/usr/bin/env python3
"""
test_kcat_predict.py - Kcat预测模块测试脚本

该脚本用于验证kcat_predict模块的核心功能
"""

import sys
import traceback
from kcat_predict import predict_kcat, batch_predict_kcat

def test_single_prediction():
    """测试单个预测功能"""
    print("🧪 测试1: 单个预测功能")
    print("-" * 40)
    
    try:
        # 测试数据
        sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
        smiles = "CCO"
        
        # 预测
        result = predict_kcat(sequence, smiles)
        
        # 验证结果
        assert isinstance(result, float), f"预期返回float类型，实际返回{type(result)}"
        assert -10 <= result <= 10, f"预测值{result}超出合理范围[-10, 10]"
        
        print(f"✅ 单个预测成功")
        print(f"   输入序列长度: {len(sequence)}")
        print(f"   输入SMILES: {smiles}")
        print(f"   预测结果: {result:.4f}")
        print(f"   Kcat值: {10**result:.2e} s⁻¹")
        return True
        
    except Exception as e:
        print(f"❌ 单个预测失败: {e}")
        traceback.print_exc()
        return False

def test_batch_prediction():
    """测试批量预测功能"""
    print("\n🧪 测试2: 批量预测功能")
    print("-" * 40)
    
    try:
        # 测试数据
        sequences = [
            "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD",
            "ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY"
        ]
        smiles_list = ["CCO", "CC(=O)O"]
        
        # 预测
        results = batch_predict_kcat(sequences, smiles_list)
        
        # 验证结果
        assert isinstance(results, list), f"预期返回list类型，实际返回{type(results)}"
        assert len(results) == len(sequences), f"结果数量{len(results)}与输入数量{len(sequences)}不匹配"
        
        for i, result in enumerate(results):
            if result is not None:
                assert isinstance(result, float), f"结果{i}预期为float类型，实际为{type(result)}"
                assert -10 <= result <= 10, f"结果{i}值{result}超出合理范围[-10, 10]"
        
        print(f"✅ 批量预测成功")
        print(f"   输入样本数: {len(sequences)}")
        print(f"   成功预测数: {len([r for r in results if r is not None])}")
        for i, result in enumerate(results):
            if result is not None:
                print(f"   样本{i+1}: {result:.4f} (Kcat: {10**result:.2e} s⁻¹)")
            else:
                print(f"   样本{i+1}: 预测失败")
        return True
        
    except Exception as e:
        print(f"❌ 批量预测失败: {e}")
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试3: 边界情况测试")
    print("-" * 40)
    
    test_cases = [
        {
            "name": "短序列",
            "sequence": "ACDEFGHIKLMNPQRSTVWY",  # 20个氨基酸
            "smiles": "C",
            "should_pass": True
        },
        {
            "name": "长序列",
            "sequence": "A" * 1000,  # 1000个氨基酸
            "smiles": "CCO",
            "should_pass": True
        },
        {
            "name": "复杂SMILES",
            "sequence": "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD",
            "smiles": "CC(C)(C)C1=CC=C(C=C1)C(=O)NC2=CC=C(C=C2)S(=O)(=O)N",
            "should_pass": True
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for case in test_cases:
        try:
            result = predict_kcat(case["sequence"], case["smiles"])
            
            if case["should_pass"]:
                assert isinstance(result, float), f"预期返回float，实际返回{type(result)}"
                assert -10 <= result <= 10, f"结果{result}超出合理范围"
                print(f"✅ {case['name']}: 通过 (结果: {result:.4f})")
                passed += 1
            else:
                print(f"❌ {case['name']}: 预期失败但成功了")
                
        except Exception as e:
            if not case["should_pass"]:
                print(f"✅ {case['name']}: 预期失败 ({str(e)[:50]}...)")
                passed += 1
            else:
                print(f"❌ {case['name']}: 预期成功但失败了 ({str(e)[:50]}...)")
    
    print(f"\n边界测试结果: {passed}/{total} 通过")
    return passed == total

def test_input_validation():
    """测试输入验证"""
    print("\n🧪 测试4: 输入验证测试")
    print("-" * 40)
    
    invalid_cases = [
        {
            "name": "空序列",
            "sequence": "",
            "smiles": "CCO"
        },
        {
            "name": "非字符串序列",
            "sequence": 123,
            "smiles": "CCO"
        },
        {
            "name": "太短序列",
            "sequence": "ACE",  # 只有3个氨基酸
            "smiles": "CCO"
        },
        {
            "name": "全部无效字符",
            "sequence": "123456789",  # 全部无效字符
            "smiles": "CCO"
        }
    ]
    
    passed = 0
    total = len(invalid_cases)
    
    for case in invalid_cases:
        try:
            result = predict_kcat(case["sequence"], case["smiles"])
            print(f"❌ {case['name']}: 预期失败但成功了 (结果: {result})")
        except Exception as e:
            print(f"✅ {case['name']}: 正确捕获异常 ({type(e).__name__})")
            passed += 1
    
    print(f"\n输入验证测试结果: {passed}/{total} 通过")
    return passed == total

def main():
    """主测试函数"""
    print("🧬 Kcat预测模块测试套件")
    print("=" * 50)
    
    tests = [
        test_single_prediction,
        test_batch_prediction,
        test_edge_cases,
        test_input_validation
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 发生异常: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"📊 测试总结: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！模块功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查模块功能。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
