#!/usr/bin/env python3
"""
预测脚本使用示例
演示如何使用predict.py进行单样本和批量预测
"""

import numpy as np
import pandas as pd
import os

def create_example_data():
    """创建示例数据"""
    print("[示例] 创建示例数据...")
    
    # 创建示例CSV文件用于批量预测
    n_samples = 5
    smiles_vectors = []
    sequence_vectors = []
    
    for i in range(n_samples):
        # 生成随机向量 (实际使用中应该是真实的分子和蛋白质向量)
        smiles_vec = np.random.rand(512)  # SMILES向量维度
        sequence_vec = np.random.rand(768)  # 序列向量维度
        
        smiles_vectors.append(','.join(map(str, smiles_vec)))
        sequence_vectors.append(','.join(map(str, sequence_vec)))
    
    # 创建DataFrame
    df = pd.DataFrame({
        'smiles_vector': smiles_vectors,
        'sequence_vector': sequence_vectors
    })
    
    # 保存到CSV
    df.to_csv('example_input.csv', index=False)
    print("[示例] 示例数据已保存到: example_input.csv")
    
    return smiles_vectors[0], sequence_vectors[0]

def demo_single_prediction():
    """演示单样本预测"""
    print("\n" + "="*50)
    print("[演示] 单样本预测")
    print("="*50)
    
    # 创建示例数据
    smiles_vec, sequence_vec = create_example_data()
    
    # 构建命令
    cmd = f"""python predict.py --mode single --device cpu \
--smiles_vector "{smiles_vec}" \
--sequence_vector "{sequence_vec}" """
    
    print(f"[命令] 执行命令:")
    print(f"python predict.py --mode single --device cpu \\")
    print(f"  --smiles_vector \"<512维向量>\" \\")
    print(f"  --sequence_vector \"<768维向量>\"")
    
    print(f"\n[执行] 运行预测...")
    os.system(cmd)

def demo_batch_prediction():
    """演示批量预测"""
    print("\n" + "="*50)
    print("[演示] 批量预测")
    print("="*50)
    
    # 检查示例文件是否存在
    if not os.path.exists('example_input.csv'):
        create_example_data()
    
    # 构建命令
    cmd = """python predict.py --mode batch --device cpu \
--input_file example_input.csv \
--output_file example_predictions.csv \
--batch_size 32"""
    
    print(f"[命令] 执行命令:")
    print(f"python predict.py --mode batch --device cpu \\")
    print(f"  --input_file example_input.csv \\")
    print(f"  --output_file example_predictions.csv \\")
    print(f"  --batch_size 32")
    
    print(f"\n[执行] 运行批量预测...")
    os.system(cmd)
    
    # 显示结果
    if os.path.exists('example_predictions.csv'):
        print(f"\n[结果] 预测结果:")
        df = pd.read_csv('example_predictions.csv')
        print(f"预测了 {len(df)} 个样本")
        print(f"预测值: {df['predicted_log10_kcat'].tolist()}")

def show_usage():
    """显示使用说明"""
    print("[使用说明] predict.py 使用方法")
    print("="*50)
    
    usage = """
1. 单样本预测:
   python predict.py --mode single --device cpu \\
     --smiles_vector "0.1,0.2,0.3,..." \\
     --sequence_vector "0.4,0.5,0.6,..."

2. 批量预测:
   python predict.py --mode batch --device cuda \\
     --input_file input.csv \\
     --output_file predictions.csv \\
     --batch_size 64

3. 输入文件格式 (CSV):
   smiles_vector,sequence_vector
   "0.1,0.2,0.3,...","0.4,0.5,0.6,..."
   "0.7,0.8,0.9,...","1.0,1.1,1.2,..."

4. 参数说明:
   --mode: 预测模式 (single/batch)
   --device: 设备选择 (auto/cpu/cuda)
   --model: 模型文件路径 (可选，默认使用最佳模型)
   --smiles_vector: SMILES分子向量 (512维)
   --sequence_vector: 蛋白质序列向量 (768维)
   --input_file: 批量预测输入文件
   --output_file: 批量预测输出文件
   --batch_size: 批量大小 (默认64)

5. 注意事项:
   - 向量总维度必须为1280 (512+768)
   - 向量值用逗号分隔
   - 批量预测时建议使用GPU加速
   - 输出为Log10_Kcat_Value的预测值
"""
    print(usage)

def main():
    """主函数"""
    print("[预测示例] Kcat预测脚本使用示例")
    print("="*50)
    
    print("\n[菜单] 选择演示:")
    print("1. 查看使用说明")
    print("2. 单样本预测演示")
    print("3. 批量预测演示")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == "1":
                show_usage()
            elif choice == "2":
                demo_single_prediction()
            elif choice == "3":
                demo_batch_prediction()
            elif choice == "4":
                print("[退出] 再见！")
                break
            else:
                print("[错误] 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n[退出] 再见！")
            break
        except Exception as e:
            print(f"[错误] 错误: {e}")

if __name__ == "__main__":
    main()
