#!/usr/bin/env python3
"""
显示散点图结果
"""

import json
import os

def show_results():
    """显示散点图生成结果"""
    results_dir = "Result/Best_Model_Plots/20250630_105402"
    
    print("[结果] R²最佳模型散点图生成结果")
    print("=" * 50)

    # 读取模型信息
    info_file = os.path.join(results_dir, "model_info.json")
    if os.path.exists(info_file):
        with open(info_file, 'r') as f:
            model_info = json.load(f)

        print(f"[路径] 模型路径: {model_info['model_path']}")
        print(f"[数据] 数据形状: {model_info['data_shape'][0]} 样本 × {model_info['data_shape'][1]} 特征")
        print(f"[数量] 预测数量: {model_info['num_predictions']}")

        print(f"\n[性能] 模型性能指标:")
        metrics = model_info['test_metrics']
        print(f"  R² (决定系数): {metrics['R²']:.4f}")
        print(f"  RMSE (均方根误差): {metrics['RMSE']:.4f}")
        print(f"  MAE (平均绝对误差): {metrics['MAE']:.4f}")
        print(f"  SCC (斯皮尔曼相关系数): {metrics['SCC']:.4f}")
        
        print(f"\n[图表] 生成的图表:")
        scatter_plot = os.path.join(results_dir, "scatter_plot.png")
        residuals_plot = os.path.join(results_dir, "residuals_plot.png")

        if os.path.exists(scatter_plot):
            print(f"  [OK] 散点图: {scatter_plot}")
            print(f"     - 显示真实值 vs 预测值的关系")
            print(f"     - 红色虚线表示完美预测线 (y=x)")
            print(f"     - 点越接近红线，预测越准确")

        if os.path.exists(residuals_plot):
            print(f"  [OK] 残差图: {residuals_plot}")
            print(f"     - 显示预测误差的分布")
            print(f"     - 理想情况下应该呈正态分布")
            print(f"     - 中心在0附近表示无系统性偏差")

        print(f"\n[解读] 结果解读:")
        r2 = metrics['R²']
        if r2 >= 0.6:
            print(f"  [优秀] 优秀: R² = {r2:.4f} ≥ 0.6，模型预测性能很好")
        elif r2 >= 0.4:
            print(f"  [良好] 良好: R² = {r2:.4f} ∈ [0.4, 0.6)，模型有一定预测能力")
        elif r2 >= 0.2:
            print(f"  [一般] 一般: R² = {r2:.4f} ∈ [0.2, 0.4)，模型预测能力有限")
        else:
            print(f"  [较差] 较差: R² = {r2:.4f} < 0.2，模型预测能力很弱")

        print(f"\n[建议] 使用建议:")
        print(f"  1. 查看散点图了解预测质量的分布")
        print(f"  2. 查看残差图检查是否存在系统性偏差")
        print(f"  3. R² = {r2:.4f} 表明模型能解释 {r2*100:.1f}% 的数据变异")

    else:
        print("[错误] 未找到模型信息文件")

    print(f"\n[位置] 所有文件位置: {results_dir}")

if __name__ == "__main__":
    show_results()
