#!/usr/bin/env python3
"""
数据预处理脚本
读取kcat_features.tsv文件，拼接smiles_vector和sequence_vector作为特征，
Log10_Kcat_Value作为标签，划分训练测试集
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import ast
import os

def parse_vector_string(vector_str):
    """解析向量字符串为numpy数组"""
    try:
        # 移除引号并解析为浮点数列表
        vector_str = vector_str.strip('"')
        vector_list = [float(x) for x in vector_str.split(',')]
        return np.array(vector_list)
    except Exception as e:
        print(f"Error parsing vector: {e}")
        return None

def load_and_preprocess_data(file_path):
    """加载和预处理数据"""
    print("Loading data...")

    # 读取TSV文件，处理引号问题
    try:
        df = pd.read_csv(file_path, sep='\t', quoting=3, on_bad_lines='skip')
    except:
        # 如果上面的方法失败，尝试其他参数
        try:
            df = pd.read_csv(file_path, sep='\t', quotechar='"', doublequote=True, on_bad_lines='skip')
        except:
            # 最后的尝试，使用最基本的参数
            df = pd.read_csv(file_path, sep='\t', on_bad_lines='skip')

    print(f"Original data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # 检查必要的列是否存在
    required_columns = ['smiles_vector', 'sequence_vector', 'Log10_Kcat_Value']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"Required column '{col}' not found in data")
    
    # 过滤掉success为FALSE的数据
    if 'success' in df.columns:
        df = df[df['success'] == True]
        print(f"After filtering success=True: {df.shape}")
    
    # 移除包含NaN的行
    df = df.dropna(subset=required_columns)
    print(f"After removing NaN: {df.shape}")
    
    print("Parsing vectors...")
    
    # 解析smiles_vector
    smiles_vectors = []
    sequence_vectors = []
    valid_indices = []
    
    for idx, row in df.iterrows():
        smiles_vec = parse_vector_string(row['smiles_vector'])
        seq_vec = parse_vector_string(row['sequence_vector'])
        
        if smiles_vec is not None and seq_vec is not None:
            smiles_vectors.append(smiles_vec)
            sequence_vectors.append(seq_vec)
            valid_indices.append(idx)
    
    print(f"Successfully parsed {len(valid_indices)} samples")
    
    if len(valid_indices) == 0:
        raise ValueError("No valid samples found after parsing vectors")
    
    # 转换为numpy数组
    smiles_vectors = np.array(smiles_vectors)
    sequence_vectors = np.array(sequence_vectors)
    
    print(f"SMILES vector shape: {smiles_vectors.shape}")
    print(f"Sequence vector shape: {sequence_vectors.shape}")
    
    # 拼接特征向量
    features = np.concatenate([smiles_vectors, sequence_vectors], axis=1)
    print(f"Combined features shape: {features.shape}")
    
    # 获取标签
    labels = df.loc[valid_indices, 'Log10_Kcat_Value'].values
    print(f"Labels shape: {labels.shape}")
    print(f"Labels range: {labels.min():.3f} to {labels.max():.3f}")
    
    return features, labels

def split_and_save_data(features, labels, test_size=0.2, random_state=42):
    """划分训练测试集并保存"""
    print("Splitting data...")
    
    X_train, X_test, y_train, y_test = train_test_split(
        features, labels, test_size=test_size, random_state=random_state
    )
    
    print(f"Training set: {X_train.shape[0]} samples")
    print(f"Test set: {X_test.shape[0]} samples")
    
    # 创建结果目录
    os.makedirs('Result', exist_ok=True)
    
    # 保存数据
    np.save('Result/train_features.npy', X_train)
    np.save('Result/train_labels.npy', y_train)
    np.save('Result/test_features.npy', X_test)
    np.save('Result/test_labels.npy', y_test)
    
    print("Data saved to Result/ directory")
    print(f"Feature dimension: {X_train.shape[1]}")
    
    return X_train, X_test, y_train, y_test

def main():
    """主函数"""
    try:
        # 加载和预处理数据
        features, labels = load_and_preprocess_data('kcat_features.tsv')
        
        # 划分和保存数据
        X_train, X_test, y_train, y_test = split_and_save_data(features, labels)
        
        print("\nData preprocessing completed successfully!")
        print(f"Training features shape: {X_train.shape}")
        print(f"Training labels shape: {y_train.shape}")
        print(f"Test features shape: {X_test.shape}")
        print(f"Test labels shape: {y_test.shape}")
        
    except Exception as e:
        print(f"Error during preprocessing: {e}")
        raise

if __name__ == "__main__":
    main()
