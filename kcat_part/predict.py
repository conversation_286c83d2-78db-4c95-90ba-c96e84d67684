#!/usr/bin/env python3
"""
Kcat预测脚本
输入smiles_vector和sequence_vector，预测Log10_Kcat_Value
支持GPU批量预测和CPU单样本预测两种模式
"""

import torch
import numpy as np
import argparse
import sys
import os
from pathlib import Path

# 添加TransCode路径
sys.path.append('TransCode')
from utils.transformer_regressor import TransformerRegressor

class KcatPredictor:
    """Kcat预测器"""

    def __init__(self, model_path=None, device='auto'):
        """
        初始化预测器

        Args:
            model_path: 模型文件路径，默认使用最佳模型
            device: 设备选择 ('auto', 'cpu', 'cuda')
        """
        # 设置设备
        if device == 'auto':
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        print(f"使用设备: {self.device}")

        # 默认模型路径
        if model_path is None:
            model_path = "Result/Transformer_Model/20250629_230536/best_model.pt"

        self.model_path = model_path
        self.model = None
        self.input_dim = 1280  # smiles_vector + sequence_vector的维度

        # 加载模型
        self._load_model()

    def _load_model(self):
        """加载预训练模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        print(f"加载模型: {self.model_path}")

        try:
            # 加载模型权重
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)

            # 创建模型
            self.model = TransformerRegressor(
                input_dim=self.input_dim,
                d_model=64,
                nhead=4,
                num_layers=2,
                dim_feedforward=256,
                dropout=0.1
            )

            # 加载权重
            self.model.load_state_dict(checkpoint)
            self.model.to(self.device)
            self.model.eval()

            print("模型加载成功")

        except Exception as e:
            raise RuntimeError(f"模型加载失败: {e}")

    def predict_single(self, smiles_vector, sequence_vector):
        """
        单样本预测

        Args:
            smiles_vector: SMILES分子向量 (numpy array)
            sequence_vector: 蛋白质序列向量 (numpy array)

        Returns:
            float: 预测的Log10_Kcat_Value
        """
        # 输入验证
        smiles_vector = np.array(smiles_vector, dtype=np.float32)
        sequence_vector = np.array(sequence_vector, dtype=np.float32)

        # 拼接特征
        features = np.concatenate([smiles_vector, sequence_vector])

        if len(features) != self.input_dim:
            raise ValueError(f"特征维度不匹配，期望 {self.input_dim}，实际 {len(features)}")

        # 转换为tensor
        features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

        # 预测
        with torch.no_grad():
            prediction = self.model(features_tensor)
            return float(prediction.cpu().numpy()[0])

    def predict_batch(self, smiles_vectors, sequence_vectors, batch_size=64):
        """
        批量预测

        Args:
            smiles_vectors: SMILES分子向量列表
            sequence_vectors: 蛋白质序列向量列表
            batch_size: 批次大小

        Returns:
            list: 预测的Log10_Kcat_Value列表
        """
        if len(smiles_vectors) != len(sequence_vectors):
            raise ValueError("smiles_vectors和sequence_vectors长度不匹配")

        n_samples = len(smiles_vectors)
        predictions = []

        print(f"开始批量预测 {n_samples} 个样本...")

        for i in range(0, n_samples, batch_size):
            end_idx = min(i + batch_size, n_samples)
            batch_smiles = smiles_vectors[i:end_idx]
            batch_sequence = sequence_vectors[i:end_idx]

            # 拼接特征
            batch_features = []
            for smiles, sequence in zip(batch_smiles, batch_sequence):
                smiles = np.array(smiles, dtype=np.float32)
                sequence = np.array(sequence, dtype=np.float32)
                features = np.concatenate([smiles, sequence])

                if len(features) != self.input_dim:
                    raise ValueError(f"样本 {i} 特征维度不匹配，期望 {self.input_dim}，实际 {len(features)}")

                batch_features.append(features)

            # 转换为tensor
            batch_tensor = torch.FloatTensor(batch_features).to(self.device)

            # 预测
            with torch.no_grad():
                batch_predictions = self.model(batch_tensor)
                predictions.extend(batch_predictions.cpu().numpy().tolist())

            if (i // batch_size + 1) % 10 == 0:
                print(f"已处理 {end_idx}/{n_samples} 个样本")

        print("批量预测完成")
        return predictions

def parse_vector_string(vector_str):
    """解析向量字符串"""
    try:
        # 移除引号并解析为浮点数列表
        vector_str = vector_str.strip('"').strip("'")
        if vector_str.startswith('[') and vector_str.endswith(']'):
            # 处理列表格式
            vector_str = vector_str[1:-1]

        vector_list = [float(x.strip()) for x in vector_str.split(',')]
        return np.array(vector_list)
    except Exception as e:
        raise ValueError(f"向量解析失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Kcat预测工具')
    parser.add_argument('--mode', choices=['single', 'batch'], default='single',
                       help='预测模式: single(单样本) 或 batch(批量)')
    parser.add_argument('--device', choices=['auto', 'cpu', 'cuda'], default='auto',
                       help='设备选择: auto(自动), cpu, cuda')
    parser.add_argument('--model', type=str, default=None,
                       help='模型文件路径，默认使用最佳模型')

    # 单样本预测参数
    parser.add_argument('--smiles_vector', type=str,
                       help='SMILES分子向量 (逗号分隔的数值)')
    parser.add_argument('--sequence_vector', type=str,
                       help='蛋白质序列向量 (逗号分隔的数值)')

    # 批量预测参数
    parser.add_argument('--input_file', type=str,
                       help='批量预测输入文件 (CSV格式)')
    parser.add_argument('--output_file', type=str,
                       help='批量预测输出文件')
    parser.add_argument('--batch_size', type=int, default=64,
                       help='批量预测的批次大小')

    args = parser.parse_args()

    try:
        # 初始化预测器
        predictor = KcatPredictor(model_path=args.model, device=args.device)

        if args.mode == 'single':
            # 单样本预测
            if not args.smiles_vector or not args.sequence_vector:
                print("错误: 单样本预测需要提供 --smiles_vector 和 --sequence_vector")
                print("\n示例用法:")
                print("python predict.py --mode single --smiles_vector '0.1,0.2,0.3,...' --sequence_vector '0.4,0.5,0.6,...'")
                return

            # 解析向量
            smiles_vec = parse_vector_string(args.smiles_vector)
            sequence_vec = parse_vector_string(args.sequence_vector)

            print(f"SMILES向量维度: {len(smiles_vec)}")
            print(f"序列向量维度: {len(sequence_vec)}")

            # 预测
            prediction = predictor.predict_single(smiles_vec, sequence_vec)

            print(f"\n预测结果:")
            print(f"Log10_Kcat_Value: {prediction:.4f}")

        elif args.mode == 'batch':
            # 批量预测
            if not args.input_file:
                print("错误: 批量预测需要提供 --input_file")
                print("\n输入文件格式 (CSV):")
                print("smiles_vector,sequence_vector")
                print("'0.1,0.2,0.3,...','0.4,0.5,0.6,...'")
                return

            # 读取输入文件
            import pandas as pd

            print(f"读取输入文件: {args.input_file}")
            df = pd.read_csv(args.input_file)

            if 'smiles_vector' not in df.columns or 'sequence_vector' not in df.columns:
                raise ValueError("输入文件必须包含 'smiles_vector' 和 'sequence_vector' 列")

            # 解析向量
            smiles_vectors = [parse_vector_string(vec) for vec in df['smiles_vector']]
            sequence_vectors = [parse_vector_string(vec) for vec in df['sequence_vector']]

            # 批量预测
            predictions = predictor.predict_batch(
                smiles_vectors, sequence_vectors, batch_size=args.batch_size
            )

            # 保存结果
            df['predicted_log10_kcat'] = predictions

            output_file = args.output_file or 'predictions.csv'
            df.to_csv(output_file, index=False)

            print(f"\n预测完成，结果保存到: {output_file}")
            print(f"预测了 {len(predictions)} 个样本")
            print(f"预测值范围: {min(predictions):.4f} 到 {max(predictions):.4f}")

    except Exception as e:
        print(f"错误: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())