# Kcat预测批处理脚本使用说明

## 概述

`kcat_predict_batch.sh` 是一个用于批量处理CSV文件的Bash脚本，能够自动读取包含蛋白质序列和化合物SMILES的CSV文件，使用kcat_predict模块进行批量预测，并将结果保存到指定的输出CSV文件中。

## 功能特性

- 🚀 **自动化处理**: 一键完成从输入到输出的整个预测流程
- 📊 **CSV格式支持**: 直接处理标准CSV文件格式
- 🔄 **批量预测**: 支持大规模数据的高效批量处理
- 📈 **结果统计**: 自动生成预测成功率和详细统计信息
- 🛡️ **错误处理**: 完善的错误检查和异常处理机制
- 🎯 **环境检查**: 自动验证运行环境和依赖项
- 📝 **详细日志**: 可选的详细输出模式

## 安装和配置

### 前置要求

1. **Conda环境**: 确保已安装并配置`kcat`环境
2. **工作目录**: 脚本需要在`/usr/XML/日常工作/朱星学/kcat`目录下运行
3. **kcat_predict模块**: 确保`kcat_predict.py`模块可用

### 脚本权限

```bash
# 添加执行权限
chmod +x kcat_predict_batch.sh
```

## 使用方法

### 基本语法

```bash
./kcat_predict_batch.sh -i <input_csv> -o <output_csv> [选项]
```

### 参数说明

#### 必需参数

- `-i, --input`: 输入CSV文件路径
- `-o, --output`: 输出CSV文件路径

#### 可选参数

- `-b, --batch`: 批处理大小 (默认: 32)
- `-v, --verbose`: 显示详细输出
- `-h, --help`: 显示帮助信息

### 使用示例

#### 基本使用
```bash
./kcat_predict_batch.sh -i data.csv -o results.csv
```

#### 自定义批次大小
```bash
./kcat_predict_batch.sh -i data.csv -o results.csv -b 64
```

#### 详细输出模式
```bash
./kcat_predict_batch.sh -i data.csv -o results.csv -v
```

#### 组合使用
```bash
./kcat_predict_batch.sh -i large_dataset.csv -o predictions.csv -b 128 -v
```

## 输入文件格式

### CSV文件要求

输入CSV文件必须包含以下列：

| 列名 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `Sequences` | 字符串 | 蛋白质序列 (单字母氨基酸代码) | `MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG` |
| `Smiles` | 字符串 | 化合物SMILES字符串 | `CCO` |

### 可选列

- 可以包含任意其他列（如ID、描述等），这些列将保留在输出文件中
- 列名大小写敏感，必须严格匹配`Sequences`和`Smiles`

### 示例输入文件

```csv
Protein_ID,Sequences,Compound_ID,Smiles,Description
P001,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG,C001,CCO,Ethanol
P002,ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY,C002,CC(=O)O,Acetic acid
P003,MGKIIGIDLGTTNSCVAIMDGTTPRVLENAEGDRTTPSIIAYTQDGETLVGQPAKRQAVTNPQNTLFAIKRLIGRRFQDEEVQRDVSIMPFKIIAADNGDAWVEVKGQKMAPPQISAEVLKKMKKTAEDYLGEPVTEAVITVPAYFNDSQRQATKDAGTIAGLNVLRIINEPTAAAIAYGLDRTGKGERNVLIFDLGGGTFDVSILTIDDGIFEVKATAGDTHLGGEDFDNRMVNHFIAEFKRKHKKDISENKRAVRRLRTACERAKRTLSSSTQASLEIDSLFEGIDFYTSITRARFEELNADLFRGTLDPVEKALRDAKLDKSQIHDIVLVGGSTRIPKIQKLLQDFFNGKELNKSINPDEAVAYGAAVQAAILSGDKSENVQDLLLLDVAPLSLGLETAGGVMTALIKRNSTIPTKQTQIFTTYSDNQPGVLIQVYEGERAMTKDNNLLGRFELSGIPPAPRGVPQIEVTFDIDANGILNVSAVDKSTGKENKITVTAYKDLNPVKQKRQKLMP,C003,C1=CC=CC=C1,Benzene
```

## 输出文件格式

### 输出CSV结构

输出文件包含原始数据的所有列，并新增以下预测结果列：

| 列名 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `Predicted_Log10_Kcat` | 浮点数 | 预测的Log10_Kcat_Value | `-0.4311` |
| `Predicted_Kcat` | 浮点数 | 预测的Kcat值 (s⁻¹) | `0.3706` |
| `Prediction_Status` | 字符串 | 预测状态 | `Success` / `Failed` |

### 示例输出文件

```csv
Protein_ID,Sequences,Compound_ID,Smiles,Description,Predicted_Log10_Kcat,Predicted_Kcat,Prediction_Status
P001,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG,C001,CCO,Ethanol,-0.4311,0.3706,Success
P002,ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY,C002,CC(=O)O,Acetic acid,-2.3973,0.0040,Success
P003,MGKIIGIDLGTTNSCVAIMDGTTPRVLENAEGDRTTPSIIAYTQDGETLVGQPAKRQAVTNPQNTLFAIKRLIGRRFQDEEVQRDVSIMPFKIIAADNGDAWVEVKGQKMAPPQISAEVLKKMKKTAEDYLGEPVTEAVITVPAYFNDSQRQATKDAGTIAGLNVLRIINEPTAAAIAYGLDRTGKGERNVLIFDLGGGTFDVSILTIDDGIFEVKATAGDTHLGGEDFDNRMVNHFIAEFKRKHKKDISENKRAVRRLRTACERAKRTLSSSTQASLEIDSLFEGIDFYTSITRARFEELNADLFRGTLDPVEKALRDAKLDKSQIHDIVLVGGSTRIPKIQKLLQDFFNGKELNKSINPDEAVAYGAAVQAAILSGDKSENVQDLLLLDVAPLSLGLETAGGVMTALIKRNSTIPTKQTQIFTTYSDNQPGVLIQVYEGERAMTKDNNLLGRFELSGIPPAPRGVPQIEVTFDIDANGILNVSAVDKSTGKENKITVTAYKDLNPVKQKRQKLMP,C003,C1=CC=CC=C1,Benzene,-2.2976,0.0050,Success
```

## 性能优化

### 批次大小调优

| 数据规模 | 推荐批次大小 | 内存需求 | 预测速度 |
|----------|-------------|----------|----------|
| < 100样本 | 16-32 | 低 | 快 |
| 100-1000样本 | 32-64 | 中等 | 中等 |
| > 1000样本 | 64-128 | 高 | 快 |

### 性能建议

1. **GPU使用**: 确保CUDA可用以加速预测
2. **内存管理**: 根据可用内存调整批次大小
3. **并行处理**: 大数据集可考虑分割后并行处理

## 错误处理和故障排除

### 常见错误

#### 1. 环境错误
```bash
[ERROR] Conda环境 'kcat' 不存在
```
**解决方案**: 确保已创建并配置kcat conda环境

#### 2. 文件格式错误
```bash
[ERROR] CSV文件缺少必需的列: ['Sequences']
```
**解决方案**: 检查CSV文件是否包含必需的`Sequences`和`Smiles`列

#### 3. 权限错误
```bash
[ERROR] 输入文件不存在: data.csv
```
**解决方案**: 检查文件路径和权限

#### 4. 内存不足
```bash
CUDA out of memory
```
**解决方案**: 减少批次大小或使用CPU模式

### 调试模式

使用`-v`参数启用详细输出：
```bash
./kcat_predict_batch.sh -i data.csv -o results.csv -v
```

### 日志信息

脚本提供彩色日志输出：
- 🔵 **[INFO]**: 一般信息
- 🟢 **[SUCCESS]**: 成功操作
- 🟡 **[WARNING]**: 警告信息
- 🔴 **[ERROR]**: 错误信息

## 高级用法

### 大规模数据处理

对于超大数据集，可以考虑分批处理：

```bash
# 分割大文件
split -l 1000 large_dataset.csv chunk_

# 批量处理
for file in chunk_*; do
    ./kcat_predict_batch.sh -i "$file" -o "result_$file" -b 128 -v
done

# 合并结果
head -1 result_chunk_aa > final_results.csv
tail -n +2 -q result_chunk_* >> final_results.csv
```

### 自动化脚本

```bash
#!/bin/bash
# 自动化处理脚本

INPUT_DIR="./input_data"
OUTPUT_DIR="./results"

mkdir -p "$OUTPUT_DIR"

for csv_file in "$INPUT_DIR"/*.csv; do
    filename=$(basename "$csv_file" .csv)
    ./kcat_predict_batch.sh -i "$csv_file" -o "$OUTPUT_DIR/${filename}_results.csv" -v
done
```

## 技术细节

### 脚本架构

1. **参数解析**: 处理命令行参数
2. **环境检查**: 验证conda环境和依赖
3. **Python脚本生成**: 动态创建处理脚本
4. **批量预测**: 调用kcat_predict模块
5. **结果统计**: 生成预测统计信息
6. **清理**: 删除临时文件

### 依赖关系

- Bash 4.0+
- Conda
- Python 3.8+
- kcat_predict模块
- pandas库

## 更新日志

- **v1.0**: 初始版本
  - 支持CSV批量处理
  - 完整的错误处理
  - 详细的日志输出
  - 自动环境检查

## 许可证

本脚本遵循与kcat_predict模块相同的许可证。

---

**最后更新**: 2024年12月  
**版本**: v1.0  
**维护者**: Kcat预测项目团队
