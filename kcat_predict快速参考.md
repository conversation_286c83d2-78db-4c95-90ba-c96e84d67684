# Kcat预测模块快速参考

## 🚀 快速开始

### 安装环境
```bash
conda activate kcat
```

### 基本预测
```python
from kcat_predict import predict_kcat

# 单个预测
log10_kcat = predict_kcat(protein_sequence, smiles)
kcat_value = 10**log10_kcat

# 批量预测
from kcat_predict import batch_predict_kcat
results = batch_predict_kcat(sequences, smiles_list)
```

## 📋 API速查

| 函数 | 用途 | 输入 | 输出 |
|------|------|------|------|
| `predict_kcat(seq, smi, verbose=False)` | 单个预测 | 序列, SMILES | Log10_Kcat_Value |
| `batch_predict_kcat(seqs, smis, verbose=False, batch_size=64)` | 批量预测 | 序列列表, SMILES列表 | Log10_Kcat_Value列表 |
| `get_feature_info()` | 特征信息 | 无 | 信息字典 |

## 🧬 输入格式

### 蛋白质序列
- **格式**: 单字母氨基酸代码
- **长度**: 8-1024个氨基酸
- **示例**: `"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"`

### 化合物SMILES
- **格式**: 标准SMILES
- **示例**: 
  - 乙醇: `"CCO"`
  - 乙酸: `"CC(=O)O"`
  - 苯: `"C1=CC=CC=C1"`

## 📊 输出解释

| Log10_Kcat | Kcat (s⁻¹) | 催化效率 |
|------------|------------|----------|
| > 3 | > 1000 | 高效 |
| 0-3 | 1-1000 | 中等 |
| < 0 | < 1 | 低效 |

**转换公式**: `Kcat = 10^(Log10_Kcat_Value)`

## 🔧 核心组件

| 组件 | 模型 | 输出维度 | 功能 |
|------|------|----------|------|
| 蛋白质特征 | ESM2+MSA Attention Fusion | 512维 | 蛋白质表示 |
| 化合物特征 | MolFormer | 768维 | 分子表示 |
| 预测模型 | Transformer回归器 | 1维 | Kcat预测 |

## ⚡ 性能指标

- **R²**: 0.3438
- **RMSE**: 1.2157  
- **MAE**: 0.9340
- **预测速度**: ~2-5秒/样本

## 🛠️ 故障排除

### 常见错误
```python
# 错误1: 序列格式错误
# 解决: 确保使用标准氨基酸代码
sequence = sequence.upper().strip()

# 错误2: 内存不足
# 解决: 减少batch_size或使用CPU
results = batch_predict_kcat(seqs, smis, batch_size=16)

# 错误3: 模型加载失败
# 解决: 检查模型文件路径
```

### 调试模式
```python
import logging
logging.basicConfig(level=logging.INFO)
result = predict_kcat(sequence, smiles, verbose=True)
```

## 📁 文件结构

```
kcat/
├── kcat_predict.py                    # 主模块
├── protein2vector/attention_fusion/   # 蛋白质特征
│   ├── seq2attention.py              # ESM2+MSA融合
│   └── ...
├── smiles2vector/                     # 化合物特征
│   └── smiles_feature.py             # MolFormer
└── kcat_part/                        # 预测模型
    └── Result/.../best_model.pt      # 最佳模型
```

## 🎯 最佳实践

1. **数据预处理**: 清理序列和SMILES格式
2. **批量处理**: 使用`batch_predict_kcat`提高效率
3. **错误处理**: 使用try-catch包装预测调用
4. **性能优化**: 在GPU上运行以加速计算

## 📞 快速帮助

- **详细文档**: 查看 `kcat_predict使用说明.md`
- **示例代码**: 运行 `python kcat_predict.py`
- **特征信息**: 调用 `get_feature_info()`

---
**版本**: v1.0 | **更新**: 2024年12月
