#!/usr/bin/env python3
"""
kcat_predict_example.py - Kcat预测模块使用示例

该示例展示了如何使用kcat_predict模块进行蛋白质-化合物相互作用的Kcat值预测
"""

from kcat_predict import predict_kcat, batch_predict_kcat

def example_single_prediction():
    """示例1: 单个预测"""
    print("=" * 60)
    print("示例1: 单个蛋白质-化合物Kcat预测")
    print("=" * 60)
    
    # 示例蛋白质序列（酶）
    protein_sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
    
    # 示例化合物SMILES
    smiles = "CCO"  # 乙醇
    
    print(f"蛋白质序列长度: {len(protein_sequence)}")
    print(f"化合物SMILES: {smiles}")
    print()
    
    # 进行预测
    try:
        kcat_value = predict_kcat(protein_sequence, smiles, verbose=True)
        print(f"预测的Log10_Kcat_Value: {kcat_value:.4f}")
        print(f"预测的Kcat值: {10**kcat_value:.2e} s⁻¹")
        
    except Exception as e:
        print(f"预测失败: {e}")

def example_batch_prediction():
    """示例2: 批量预测"""
    print("\n" + "=" * 60)
    print("示例2: 批量Kcat预测")
    print("=" * 60)
    
    # 多个蛋白质序列
    sequences = [
        "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD",
        "ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY",
        "MGKIIGIDLGTTNSCVAIMDGTTPRVLENAEGDRTTPSIIAYTQDGETLVGQPAKRQAVTNPQNTLFAIKRLIGRRFQDEEVQRDVSIMPFKIIAADNGDAWVEVKGQKMAPPQISAEVLKKMKKTAEDYLGEPVTEAVITVPAYFNDSQRQATKDAGTIAGLNVLRIINEPTAAAIAYGLDRTGKGERNVLIFDLGGGTFDVSILTIDDGIFEVKATAGDTHLGGEDFDNRMVNHFIAEFKRKHKKDISENKRAVRRLRTACERAKRTLSSSTQASLEIDSLFEGIDFYTSITRARFEELNADLFRGTLDPVEKALRDAKLDKSQIHDIVLVGGSTRIPKIQKLLQDFFNGKELNKSINPDEAVAYGAAVQAAILSGDKSENVQDLLLLDVAPLSLGLETAGGVMTALIKRNSTIPTKQTQIFTTYSDNQPGVLIQVYEGERAMTKDNNLLGRFELSGIPPAPRGVPQIEVTFDIDANGILNVSAVDKSTGKENKITVTAYKDLNPVKQKRQKLMP"
    ]
    
    # 多个化合物SMILES
    smiles_list = [
        "CCO",                    # 乙醇
        "CC(=O)O",               # 乙酸
        "C1=CC=CC=C1"            # 苯
    ]
    
    print(f"待预测样本数: {len(sequences)}")
    print()
    
    # 批量预测
    try:
        results = batch_predict_kcat(sequences, smiles_list, verbose=True)
        
        print("\n预测结果:")
        print("-" * 50)
        for i, (seq, smi, result) in enumerate(zip(sequences, smiles_list, results)):
            if result is not None:
                print(f"样本 {i+1}:")
                print(f"  序列长度: {len(seq)}")
                print(f"  SMILES: {smi}")
                print(f"  Log10_Kcat: {result:.4f}")
                print(f"  Kcat值: {10**result:.2e} s⁻¹")
            else:
                print(f"样本 {i+1}: 预测失败")
            print()
            
    except Exception as e:
        print(f"批量预测失败: {e}")

def example_custom_usage():
    """示例3: 自定义使用"""
    print("\n" + "=" * 60)
    print("示例3: 自定义使用场景")
    print("=" * 60)
    
    # 模拟从文件读取数据
    import pandas as pd
    
    # 创建示例数据
    data = {
        'protein_id': ['P1', 'P2', 'P3'],
        'sequence': [
            "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD",
            "ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY",
            "MGKIIGIDLGTTNSCVAIMDGTTPRVLENAEGDRTTPSIIAYTQDGETLVGQPAKRQAVTNPQNTLFAIKRLIGRRFQDEEVQRDVSIMPFKIIAADNGDAWVEVKGQKMAPPQISAEVLKKMKKTAEDYLGEPVTEAVITVPAYFNDSQRQATKDAGTIAGLNVLRIINEPTAAAIAYGLDRTGKGERNVLIFDLGGGTFDVSILTIDDGIFEVKATAGDTHLGGEDFDNRMVNHFIAEFKRKHKKDISENKRAVRRLRTACERAKRTLSSSTQASLEIDSLFEGIDFYTSITRARFEELNADLFRGTLDPVEKALRDAKLDKSQIHDIVLVGGSTRIPKIQKLLQDFFNGKELNKSINPDEAVAYGAAVQAAILSGDKSENVQDLLLLDVAPLSLGLETAGGVMTALIKRNSTIPTKQTQIFTTYSDNQPGVLIQVYEGERAMTKDNNLLGRFELSGIPPAPRGVPQIEVTFDIDANGILNVSAVDKSTGKENKITVTAYKDLNPVKQKRQKLMP"
        ],
        'compound_id': ['C1', 'C2', 'C3'],
        'smiles': ['CCO', 'CC(=O)O', 'C1=CC=CC=C1']
    }
    
    df = pd.DataFrame(data)
    print("输入数据:")
    print(df[['protein_id', 'compound_id', 'smiles']])
    print()
    
    # 批量预测
    predictions = batch_predict_kcat(df['sequence'].tolist(), df['smiles'].tolist())
    
    # 添加预测结果到DataFrame
    df['predicted_log10_kcat'] = predictions
    df['predicted_kcat'] = [10**p if p is not None else None for p in predictions]
    
    print("预测结果:")
    print(df[['protein_id', 'compound_id', 'predicted_log10_kcat', 'predicted_kcat']])

def main():
    """主函数"""
    print("🧬 Kcat预测模块使用示例")
    print("基于ESM2+MSA蛋白质特征和MolFormer化合物特征的Transformer预测模型")
    print()
    
    # 运行示例
    example_single_prediction()
    example_batch_prediction()
    example_custom_usage()
    
    print("\n" + "=" * 60)
    print("✅ 所有示例运行完成！")
    print("=" * 60)
    
    print("\n📖 使用说明:")
    print("1. 单个预测: predict_kcat(protein_sequence, smiles)")
    print("2. 批量预测: batch_predict_kcat(sequences, smiles_list)")
    print("3. 输入要求:")
    print("   - 蛋白质序列: 标准单字母氨基酸代码")
    print("   - SMILES: 标准SMILES格式的化合物结构")
    print("4. 输出: Log10_Kcat_Value (对数值)")
    print("5. 转换为Kcat值: kcat = 10^(log10_kcat) s⁻¹")

if __name__ == "__main__":
    main()
