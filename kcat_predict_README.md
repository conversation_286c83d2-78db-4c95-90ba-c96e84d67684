# Kcat预测模块 (kcat_predict.py)

## 概述

`kcat_predict.py` 是一个独立的、完整的Kcat预测模块，整合了蛋白质序列特征提取、化合物分子特征提取和基于Transformer的预测模型，可以预测蛋白质-化合物相互作用的Log10_Kcat_Value。

## 核心功能

### 1. 蛋白质特征提取
- **ESM-2模型**: 使用Facebook的ESM-2 (650M参数)提取蛋白质序列的深度特征
- **本地模型支持**: 优先使用本地缓存的模型，支持离线运行
- **备选方案**: 如果模型加载失败，自动使用基于序列的伪特征

### 2. 化合物特征提取  
- **MolFormer模型**: 使用预训练的MolFormer模型提取SMILES分子特征
- **本地模型**: 使用本地配置的MolFormer模型 (`/usr/XML/database/MolFormer`)
- **特征维度**: 768维分子特征向量

### 3. Kcat预测
- **Transformer回归器**: 基于注意力机制的深度学习模型
- **预训练模型**: 使用已训练好的最佳模型 (R²=0.3438)
- **特征融合**: 将蛋白质特征(512维) + 化合物特征(768维) = 1280维输入特征

## 安装要求

### 环境配置
```bash
conda activate kcat
```

### 依赖包
- `torch` - PyTorch深度学习框架
- `transformers` - Hugging Face Transformers库
- `numpy` - 数值计算
- `fair-esm` - Facebook ESM模型库 (可选)
- `pandas` - 数据处理 (示例中使用)

### 模型文件
- ESM-2模型: 本地缓存在 `/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/`
- MolFormer模型: `/usr/XML/database/MolFormer`
- Kcat预测模型: `kcat_part/Result/Transformer_Model/20250629_230536/best_model.pt`

## 使用方法

### 1. 基本使用

```python
from kcat_predict import predict_kcat

# 蛋白质序列 (单字母氨基酸代码)
sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"

# 化合物SMILES
smiles = "CCO"  # 乙醇

# 预测Kcat值
log10_kcat = predict_kcat(sequence, smiles)
kcat_value = 10**log10_kcat

print(f"预测的Log10_Kcat_Value: {log10_kcat:.4f}")
print(f"预测的Kcat值: {kcat_value:.2e} s⁻¹")
```

### 2. 批量预测

```python
from kcat_predict import batch_predict_kcat

sequences = [
    "MPIRVPDELPAVNFLREENVF...",  # 蛋白质序列1
    "ACDEFGHIKLMNPQRSTVWY...",   # 蛋白质序列2
]

smiles_list = [
    "CCO",        # 乙醇
    "CC(=O)O",    # 乙酸
]

results = batch_predict_kcat(sequences, smiles_list, verbose=True)
print(f"批量预测结果: {results}")
```

### 3. 详细示例

运行示例脚本查看更多用法：
```bash
python kcat_predict_example.py
```

## API参考

### predict_kcat(protein_sequence, smiles, verbose=False)

预测单个蛋白质-化合物相互作用的Kcat值。

**参数:**
- `protein_sequence` (str): 蛋白质序列，单字母氨基酸代码
- `smiles` (str): 化合物SMILES字符串
- `verbose` (bool): 是否显示详细日志信息

**返回:**
- `float`: 预测的Log10_Kcat_Value

**异常:**
- `TypeError`: 输入类型错误
- `ValueError`: 序列无效或太短
- `RuntimeError`: 预测过程失败

### batch_predict_kcat(sequences, smiles_list, verbose=False)

批量预测多个蛋白质-化合物相互作用的Kcat值。

**参数:**
- `sequences` (List[str]): 蛋白质序列列表
- `smiles_list` (List[str]): SMILES字符串列表
- `verbose` (bool): 是否显示详细日志信息

**返回:**
- `List[float]`: 预测的Log10_Kcat_Value列表，失败的预测返回None

## 模型信息

### 预训练模型性能
- **R²**: 0.3438
- **RMSE**: 1.2157
- **MAE**: 0.9340
- **训练样本**: 14,434
- **测试样本**: 3,609

### 模型架构
- **输入维度**: 1280 (蛋白质512 + 化合物768)
- **Transformer层数**: 2
- **注意力头数**: 4
- **隐藏层维度**: 64
- **前馈网络维度**: 256

## 输入要求

### 蛋白质序列
- **格式**: 单字母氨基酸代码 (A, C, D, E, F, G, H, I, K, L, M, N, P, Q, R, S, T, V, W, Y)
- **长度**: 最少8个氨基酸，最多1024个氨基酸
- **清理**: 自动移除非标准氨基酸字符

### 化合物SMILES
- **格式**: 标准SMILES格式
- **示例**: 
  - 乙醇: `CCO`
  - 乙酸: `CC(=O)O`
  - 苯: `C1=CC=CC=C1`

## 输出说明

### Log10_Kcat_Value
- **含义**: Kcat值的常用对数 (log₁₀)
- **单位**: 无量纲
- **转换**: Kcat = 10^(Log10_Kcat_Value) s⁻¹

### 典型值范围
- **Log10_Kcat_Value**: 通常在 -2 到 6 之间
- **Kcat值**: 通常在 0.01 到 10⁶ s⁻¹ 之间

## 故障排除

### 常见问题

1. **ESM-2模型加载失败**
   - 检查网络连接
   - 确认本地模型缓存路径
   - 程序会自动使用备选特征

2. **MolFormer模型加载失败**
   - 检查模型路径: `/usr/XML/database/MolFormer`
   - 确认模型文件完整性

3. **预训练模型加载失败**
   - 检查模型文件: `kcat_part/Result/Transformer_Model/20250629_230536/best_model.pt`
   - 程序会使用随机初始化的模型

4. **内存不足**
   - 使用CPU模式: 设置环境变量 `CUDA_VISIBLE_DEVICES=""`
   - 减少批量预测的批次大小

### 日志信息

启用详细日志查看运行状态：
```python
predict_kcat(sequence, smiles, verbose=True)
```

## 性能优化

1. **GPU加速**: 自动检测并使用GPU
2. **模型缓存**: 模型只加载一次，后续调用复用
3. **批量处理**: 批量预测比逐个预测更高效

## 文件结构

```
kcat_predict.py              # 主模块文件
kcat_predict_example.py      # 使用示例
kcat_predict_README.md       # 说明文档
kcat_part/                   # 预训练模型目录
├── Result/
│   └── Transformer_Model/
│       └── 20250629_230536/
│           └── best_model.pt
```

## 更新日志

- **v1.0**: 初始版本，整合ESM2、MolFormer和Transformer预测模型
- 支持本地模型加载和离线运行
- 提供单个预测和批量预测接口
- 完整的错误处理和备选方案

## 联系信息

如有问题或建议，请联系开发团队。
