# Kcat预测模块使用说明

## 概述

`kcat_predict.py` 是一个基于深度学习的酶催化效率（Kcat）预测模块，能够根据蛋白质序列和化合物SMILES预测Log10_Kcat_Value。该模块严格使用以下原始核心组件：

- **蛋白质特征提取**: `protein2vector/attention_fusion/seq2attention.py` (ESM2+MSA attention fusion)
- **化合物特征提取**: `smiles2vector/smiles_feature.py` (MolFormer)
- **预测模型**: `kcat_part/predict.py` (最佳预训练Transformer模型，R²=0.3438)

## 核心特性

### 🧬 先进的蛋白质特征提取
- **ESM2模型**: Facebook的ESM-2 (650M参数) 提取深度蛋白质表示
- **MSA特征**: ESM MSA Transformer (100M参数) 提取进化信息
- **Attention Fusion**: 交叉注意力机制融合ESM2和MSA特征
- **输出维度**: 512维融合特征向量

### 🧪 高质量化合物特征提取
- **MolFormer模型**: 预训练的分子Transformer模型
- **SMILES处理**: 标准SMILES格式分子结构编码
- **输出维度**: 768维分子特征向量

### 🤖 最佳预测模型
- **模型架构**: Transformer回归器
- **性能指标**: R²=0.3438, RMSE=1.2157, MAE=0.9340
- **训练数据**: 14,434个训练样本，3,609个测试样本
- **特征融合**: 1280维输入 (768维SMILES + 512维蛋白质)

## 安装要求

### 环境配置
```bash
# 激活conda环境
conda activate kcat
```

### 依赖包
- `torch` - PyTorch深度学习框架
- `transformers` - Hugging Face Transformers库
- `fair-esm` - Facebook ESM模型库
- `numpy` - 数值计算
- `biopython` - 生物信息学工具
- `einops` - 张量操作库

### 模型文件
确保以下模型文件存在：
- ESM2模型: `/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/`
- MolFormer模型: `/usr/XML/database/MolFormer`
- Kcat预测模型: `kcat_part/Result/Transformer_Model/20250629_230536/best_model.pt`

## 快速开始

### 基本使用

```python
from kcat_predict import predict_kcat

# 蛋白质序列 (单字母氨基酸代码)
sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"

# 化合物SMILES
smiles = "CCO"  # 乙醇

# 预测Kcat值
log10_kcat = predict_kcat(sequence, smiles)
kcat_value = 10**log10_kcat

print(f"预测的Log10_Kcat_Value: {log10_kcat:.4f}")
print(f"预测的Kcat值: {kcat_value:.2e} s⁻¹")
```

### 批量预测

```python
from kcat_predict import batch_predict_kcat

# 多个蛋白质序列
sequences = [
    "MPIRVPDELPAVNFLREENVF...",  # 蛋白质序列1
    "ACDEFGHIKLMNPQRSTVWY...",   # 蛋白质序列2
    "MGKIIGIDLGTTNSCVAIMD...",   # 蛋白质序列3
]

# 多个化合物SMILES
smiles_list = [
    "CCO",        # 乙醇
    "CC(=O)O",    # 乙酸
    "C1=CC=CC=C1" # 苯
]

# 批量预测
results = batch_predict_kcat(sequences, smiles_list, verbose=True, batch_size=32)

# 处理结果
for i, result in enumerate(results):
    print(f"样本 {i+1}: Log10_Kcat = {result:.4f}, Kcat = {10**result:.2e} s⁻¹")
```

### 详细模式

```python
from kcat_predict import predict_kcat, get_feature_info

# 查看特征提取信息
feature_info = get_feature_info()
for key, value in feature_info.items():
    print(f"{key}: {value}")

# 详细预测过程
result = predict_kcat(sequence, smiles, verbose=True)
```

## API参考

### predict_kcat(protein_sequence, smiles, verbose=False)

预测单个蛋白质-化合物相互作用的Kcat值。

**参数:**
- `protein_sequence` (str): 蛋白质序列，单字母氨基酸代码
- `smiles` (str): 化合物SMILES字符串
- `verbose` (bool): 是否显示详细日志信息，默认False

**返回:**
- `float`: 预测的Log10_Kcat_Value

**异常:**
- `RuntimeError`: 严格模式下任何组件失败时抛出，不允许降级

**示例:**
```python
log10_kcat = predict_kcat("MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG", "CCO")
```

### batch_predict_kcat(sequences, smiles_list, verbose=False, batch_size=64)

批量预测多个蛋白质-化合物相互作用的Kcat值。

**参数:**
- `sequences` (List[str]): 蛋白质序列列表
- `smiles_list` (List[str]): SMILES字符串列表
- `verbose` (bool): 是否显示详细日志信息，默认False
- `batch_size` (int): 批次大小，默认64

**返回:**
- `List[float]`: 预测的Log10_Kcat_Value列表

**异常:**
- `ValueError`: 序列列表和SMILES列表长度不匹配
- `RuntimeError`: 严格模式下任何组件失败时抛出

### get_feature_info()

获取当前使用的特征提取方法信息。

**返回:**
- `Dict[str, str]`: 特征提取方法信息字典

## 输入要求

### 蛋白质序列
- **格式**: 单字母氨基酸代码 (A, C, D, E, F, G, H, I, K, L, M, N, P, Q, R, S, T, V, W, Y)
- **长度**: 建议8-1024个氨基酸
- **清理**: 自动移除非标准氨基酸字符
- **示例**: 
  ```
  "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
  ```

### 化合物SMILES
- **格式**: 标准SMILES格式
- **支持**: 有机小分子化合物
- **示例**:
  - 乙醇: `CCO`
  - 乙酸: `CC(=O)O`
  - 苯: `C1=CC=CC=C1`
  - 葡萄糖: `C([C@@H]1[C@H]([C@@H]([C@H]([C@H](O1)O)O)O)O)O`

## 输出说明

### Log10_Kcat_Value
- **含义**: Kcat值的常用对数 (log₁₀)
- **单位**: 无量纲
- **转换**: Kcat = 10^(Log10_Kcat_Value) s⁻¹
- **典型范围**: -2 到 6 之间

### Kcat值
- **含义**: 酶催化常数，表示酶的催化效率
- **单位**: s⁻¹ (每秒)
- **典型范围**: 0.01 到 10⁶ s⁻¹

### 解释指南
- **Log10_Kcat > 3**: 高催化效率
- **Log10_Kcat 0-3**: 中等催化效率  
- **Log10_Kcat < 0**: 低催化效率

## 性能特性

### 模型性能
- **R²**: 0.3438 (解释34.38%的方差)
- **RMSE**: 1.2157 (均方根误差)
- **MAE**: 0.9340 (平均绝对误差)

### 计算性能
- **GPU加速**: 自动检测并使用CUDA
- **内存需求**: 建议8GB+ GPU内存
- **预测速度**: 
  - 单个预测: ~2-5秒
  - 批量预测: ~0.1-0.5秒/样本

### 特征质量
- **ESM2特征**: 高质量蛋白质表示
- **MSA特征**: 70-80%功能正常，提供进化信息
- **Attention Fusion**: 交叉注意力融合机制
- **MolFormer特征**: 先进的分子表示

## 故障排除

### 常见问题

#### 1. 模型加载失败
```
错误: 无法加载ESM-2模型
解决: 检查网络连接或本地模型缓存
```

#### 2. MSA特征警告
```
警告: MSA Transformer extraction failed
说明: MSA模型仍在工作，使用传统MSA作为补充
影响: 轻微影响特征质量，但仍优于无MSA
```

#### 3. 内存不足
```
错误: CUDA out of memory
解决: 
- 减少batch_size
- 使用CPU模式: 设置环境变量 CUDA_VISIBLE_DEVICES=""
```

#### 4. 序列格式错误
```
错误: 序列不包含有效的氨基酸
解决: 确保使用标准单字母氨基酸代码
```

### 调试模式

启用详细日志查看运行状态：
```python
import logging
logging.basicConfig(level=logging.INFO)

result = predict_kcat(sequence, smiles, verbose=True)
```

### 性能优化

1. **批量处理**: 使用`batch_predict_kcat`而非循环调用`predict_kcat`
2. **GPU使用**: 确保CUDA可用以加速计算
3. **内存管理**: 适当调整batch_size避免内存溢出

## 严格模式说明

本模块运行在**严格模式**下，具有以下特点：

- ✅ **不允许降级**: 所有原始组件必须正常工作
- ✅ **保证质量**: 确保使用最高质量的特征和模型
- ✅ **错误透明**: 任何组件失败都会明确报告
- ✅ **性能保证**: 严格使用最佳预训练模型

如果任何核心组件失败，程序会抛出`RuntimeError`而不是静默降级。

## 引用和致谢

如果您在研究中使用了此模块，请引用相关论文：

- ESM-2: Lin et al. "Language models of protein sequences at the scale of evolution enable accurate structure prediction"
- MSA Transformer: Rao et al. "MSA Transformer"  
- MolFormer: Ross et al. "Large-scale chemical language representations capture molecular structure and properties"

## 更新日志

- **v1.0**: 初始版本，整合ESM2、MSA、MolFormer和Transformer预测模型
- 支持严格模式，不允许降级
- 完整的错误处理和性能优化

## 高级用法

### 自定义模型路径

```python
from kcat_predict import StrictKcatPredictorComplete

# 使用自定义模型路径
predictor = StrictKcatPredictorComplete(
    model_path="path/to/your/model.pt",
    device="cuda"  # 或 "cpu", "auto"
)

result = predictor.predict(sequence, smiles)
```

### 特征向量提取

```python
from kcat_predict import StrictProteinFeatureExtractor, StrictSMILESFeatureExtractor

# 单独提取蛋白质特征
protein_extractor = StrictProteinFeatureExtractor()
protein_features = protein_extractor.extract_sequence_features(sequence)
print(f"蛋白质特征维度: {protein_features.shape}")  # (512,)

# 单独提取SMILES特征
smiles_extractor = StrictSMILESFeatureExtractor()
smiles_features = smiles_extractor.extract_smiles_features(smiles)
print(f"SMILES特征维度: {smiles_features.shape}")  # (768,)
```

### 数据处理示例

```python
import pandas as pd
from kcat_predict import batch_predict_kcat

# 从CSV文件读取数据
df = pd.read_csv("enzyme_data.csv")

# 批量预测
results = batch_predict_kcat(
    df['protein_sequence'].tolist(),
    df['compound_smiles'].tolist(),
    batch_size=32,
    verbose=True
)

# 保存结果
df['predicted_log10_kcat'] = results
df['predicted_kcat'] = [10**r if r is not None else None for r in results]
df.to_csv("predictions.csv", index=False)
```

## 最佳实践

### 1. 数据预处理
```python
def preprocess_sequence(sequence):
    """预处理蛋白质序列"""
    # 移除空格和换行符
    sequence = sequence.replace(" ", "").replace("\n", "")
    # 转换为大写
    sequence = sequence.upper()
    # 验证氨基酸
    valid_aa = set('ACDEFGHIKLMNPQRSTVWY')
    if not all(aa in valid_aa for aa in sequence):
        raise ValueError("序列包含无效氨基酸")
    return sequence

def preprocess_smiles(smiles):
    """预处理SMILES字符串"""
    # 移除空格
    smiles = smiles.strip()
    # 基本验证
    if not smiles or len(smiles) < 2:
        raise ValueError("SMILES字符串无效")
    return smiles
```

### 2. 错误处理
```python
def safe_predict(sequence, smiles):
    """安全的预测函数"""
    try:
        # 预处理
        sequence = preprocess_sequence(sequence)
        smiles = preprocess_smiles(smiles)

        # 预测
        result = predict_kcat(sequence, smiles)
        return {
            'success': True,
            'log10_kcat': result,
            'kcat': 10**result,
            'error': None
        }
    except Exception as e:
        return {
            'success': False,
            'log10_kcat': None,
            'kcat': None,
            'error': str(e)
        }
```

### 3. 性能监控
```python
import time
from kcat_predict import predict_kcat

def benchmark_prediction(sequence, smiles, n_runs=10):
    """性能基准测试"""
    times = []

    for i in range(n_runs):
        start_time = time.time()
        result = predict_kcat(sequence, smiles)
        end_time = time.time()
        times.append(end_time - start_time)

    print(f"平均预测时间: {np.mean(times):.3f}s")
    print(f"标准差: {np.std(times):.3f}s")
    print(f"最快: {min(times):.3f}s")
    print(f"最慢: {max(times):.3f}s")

    return result
```

## 技术细节

### 模型架构
```
输入层 (1280维)
    ↓
嵌入层 (64维) + LayerNorm
    ↓
Transformer编码器 (2层)
    ├── 多头注意力 (4头)
    ├── 前馈网络 (256维)
    └── Dropout (0.1)
    ↓
回归器
    ├── Linear (64→32)
    ├── ReLU
    └── Linear (32→1)
    ↓
输出 (Log10_Kcat_Value)
```

### 特征融合流程
```
蛋白质序列 → ESM2 (1280维) ┐
                           ├→ Attention Fusion → 512维
MSA序列 → MSA Transformer (768维) ┘

化合物SMILES → MolFormer → 768维

最终特征 = concat([SMILES特征(768), 蛋白质特征(512)]) = 1280维
```

### 计算复杂度
- **ESM2特征提取**: O(L²) where L = 序列长度
- **MSA特征提取**: O(M×L²) where M = MSA深度
- **MolFormer特征提取**: O(S²) where S = SMILES长度
- **Transformer预测**: O(1) 固定输入维度

## 限制和注意事项

### 模型限制
1. **序列长度**: 建议不超过1024个氨基酸
2. **SMILES复杂度**: 非常复杂的大分子可能效果不佳
3. **训练数据偏差**: 模型性能依赖于训练数据的代表性
4. **预测范围**: 主要适用于酶-底物相互作用

### 使用注意事项
1. **计算资源**: 需要足够的GPU内存 (建议8GB+)
2. **网络连接**: 首次运行需要下载模型文件
3. **数据质量**: 输入数据质量直接影响预测准确性
4. **结果解释**: 预测值应结合生物学知识进行解释

### 已知问题
1. **MSA处理**: 部分MSA处理步骤可能失败，但不影响整体功能
2. **内存使用**: 长序列或大批量可能导致内存不足
3. **模型更新**: 当前使用固定的预训练模型，无法在线学习

## 扩展开发

### 自定义特征提取器
```python
class CustomProteinExtractor:
    def extract_sequence_features(self, sequence):
        # 实现自定义蛋白质特征提取
        pass

# 集成到预测器中
predictor = StrictKcatPredictorComplete()
predictor.protein_extractor = CustomProteinExtractor()
```

### 模型微调
```python
# 注意: 当前版本不支持在线训练
# 如需微调，请参考原始训练代码
```

## 版本兼容性

- **Python**: 3.8+
- **PyTorch**: 1.12+
- **Transformers**: 4.20+
- **CUDA**: 11.0+ (可选，用于GPU加速)

## 许可证

本项目遵循相应的开源许可证。使用前请确保遵守相关条款。

## 联系信息

如有问题或建议，请联系开发团队。

---

**最后更新**: 2024年12月
**版本**: v1.0
**维护者**: Kcat预测项目团队
