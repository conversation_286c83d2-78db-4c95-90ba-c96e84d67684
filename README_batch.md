# Kcat预测批处理脚本 - 精简版

## 使用方法

```bash
./kcat_predict_batch.sh -i <input.csv> -o <output.csv>
```

## 参数说明

- `-i`: 输入CSV文件路径
- `-o`: 输出CSV文件路径

## 输入文件格式

CSV文件必须包含以下列：
- `Sequences`: 蛋白质序列（单字母氨基酸代码）
- `Smiles`: 化合物SMILES字符串

可以包含其他列，将保留在输出中。

## 输出文件格式

输出文件包含原始数据的所有列，并新增：
- `Predicted_Log10_Kcat`: 预测的Log10_Kcat_Value
- `Predicted_Kcat`: 预测的Kcat值 (s⁻¹)
- `Prediction_Status`: 预测状态 (Success/Failed)

## 示例

### 输入文件 (data.csv)
```csv
Protein_ID,Sequences,Compound_ID,Smiles,Description
P001,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG,C001,CCO,Ethanol
P002,ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY,C002,CC(=O)O,Acetic acid
```

### 运行命令
```bash
./kcat_predict_batch.sh -i data.csv -o results.csv
```

### 输出文件 (results.csv)
```csv
Protein_ID,Sequences,Compound_ID,Smiles,Description,Predicted_Log10_Kcat,Predicted_Kcat,Prediction_Status
P001,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG,C001,CCO,Ethanol,0.4803,3.0218,Success
P002,ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY,C002,CC(=O)O,Acetic acid,3.6616,4588.2047,Success
```

## 环境要求

- Conda环境: `kcat`
- 工作目录: `/usr/XML/日常工作/朱星学/kcat`
- Python模块: `kcat_predict`

## 注意事项

1. 脚本会自动切换到工作目录 `/usr/XML/日常工作/朱星学/kcat`
2. 使用conda环境 `kcat` 运行预测
3. 输入文件必须是CSV格式且包含必需的列
4. 输出目录会自动创建（如果不存在）
5. 临时Python脚本会在执行完成后自动清理
