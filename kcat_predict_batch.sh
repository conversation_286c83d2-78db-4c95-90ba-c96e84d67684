#!/bin/bash

# Kcat预测批处理脚本 - 精简版
set -e

# 配置
KCAT_ENV="kcat"
WORK_DIR="/usr/XML/日常工作/朱星学/kcat"

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -i) INPUT_FILE="$2"; shift 2 ;;
        -o) OUTPUT_FILE="$2"; shift 2 ;;
        *) echo "用法: $0 -i <input.csv> -o <output.csv>"; exit 1 ;;
    esac
done

# 检查参数
if [[ -z "$INPUT_FILE" || -z "$OUTPUT_FILE" ]]; then
    echo "用法: $0 -i <input.csv> -o <output.csv>"
    exit 1
fi

if [[ ! -f "$INPUT_FILE" ]]; then
    echo "错误: 输入文件不存在: $INPUT_FILE"
    exit 1
fi

# 转换为绝对路径
# INPUT_FILE=$(realpath "$INPUT_FILE")
# OUTPUT_FILE=$(realpath "$OUTPUT_FILE")

# 创建输出目录
mkdir -p "$(dirname "$OUTPUT_FILE")"

# 切换到工作目录
cd "$WORK_DIR"

# 执行预测
echo "🧬 开始Kcat预测..."
conda run -n "$KCAT_ENV" python kcat_predict.py -i "$INPUT_FILE" -o "$OUTPUT_FILE"

echo "✅ 预测完成! 结果保存到: $OUTPUT_FILE"
