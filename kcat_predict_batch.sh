#!/bin/bash

# Kcat预测批处理脚本
# 作者: Kcat预测项目团队
# 版本: v1.0
# 描述: 批量处理CSV文件中的蛋白质序列和化合物SMILES，预测Kcat值

# 设置错误处理
set -e

# 脚本配置
SCRIPT_NAME=$(basename "$0")
KCAT_ENV="kcat"
WORK_DIR="/usr/XML/日常工作/朱星学/kcat"
PYTHON_SCRIPT="kcat_predict_csv.py"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
🧬 Kcat预测批处理脚本

用法: $SCRIPT_NAME -i <input_csv> -o <output_csv> [选项]

必需参数:
  -i, --input     输入CSV文件路径 (必须包含 'Sequences' 和 'Smiles' 列)
  -o, --output    输出CSV文件路径

可选参数:
  -b, --batch     批处理大小 (默认: 32)
  -v, --verbose   显示详细输出
  -h, --help      显示此帮助信息

输入CSV格式要求:
  - 必须包含 'Sequences' 列: 蛋白质序列 (单字母氨基酸代码)
  - 必须包含 'Smiles' 列: 化合物SMILES字符串
  - 可以包含其他列，将保留在输出中

输出CSV格式:
  - 包含原始数据的所有列
  - 新增 'Predicted_Log10_Kcat' 列: 预测的Log10_Kcat_Value
  - 新增 'Predicted_Kcat' 列: 预测的Kcat值 (s⁻¹)
  - 新增 'Prediction_Status' 列: 预测状态 (Success/Failed)

示例:
  $SCRIPT_NAME -i data.csv -o results.csv
  $SCRIPT_NAME -i data.csv -o results.csv -b 64 -v

环境要求:
  - Conda环境: $KCAT_ENV
  - 工作目录: $WORK_DIR
  - Python模块: kcat_predict

EOF
}

# 检查参数
check_arguments() {
    if [[ -z "$INPUT_FILE" ]]; then
        log_error "缺少输入文件参数 (-i)"
        show_help
        exit 1
    fi
    
    if [[ -z "$OUTPUT_FILE" ]]; then
        log_error "缺少输出文件参数 (-o)"
        show_help
        exit 1
    fi
    
    if [[ ! -f "$INPUT_FILE" ]]; then
        log_error "输入文件不存在: $INPUT_FILE"
        exit 1
    fi
    
    # 检查输入文件扩展名
    if [[ ! "$INPUT_FILE" =~ \.csv$ ]]; then
        log_warning "输入文件不是CSV格式，将尝试作为CSV处理"
    fi
    
    # 检查输出目录是否存在
    OUTPUT_DIR=$(dirname "$OUTPUT_FILE")
    if [[ ! -d "$OUTPUT_DIR" ]]; then
        log_info "创建输出目录: $OUTPUT_DIR"
        mkdir -p "$OUTPUT_DIR"
    fi
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查conda
    if ! command -v conda &> /dev/null; then
        log_error "Conda未安装或不在PATH中"
        exit 1
    fi
    
    # 检查conda环境
    if ! conda env list | grep -q "^$KCAT_ENV "; then
        log_error "Conda环境 '$KCAT_ENV' 不存在"
        log_info "请先创建并配置kcat环境"
        exit 1
    fi
    
    # 检查工作目录
    if [[ ! -d "$WORK_DIR" ]]; then
        log_error "工作目录不存在: $WORK_DIR"
        exit 1
    fi
    
    # 检查kcat_predict模块
    if [[ ! -f "$WORK_DIR/kcat_predict.py" ]]; then
        log_error "kcat_predict.py模块不存在: $WORK_DIR/kcat_predict.py"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 创建Python处理脚本
create_python_script() {
    log_info "创建Python处理脚本..."
    
    cat > "$WORK_DIR/$PYTHON_SCRIPT" << 'EOF'
#!/usr/bin/env python3
"""
CSV批处理脚本 - Kcat预测
"""

import pandas as pd
import sys
import argparse
import logging
from pathlib import Path
import time
from kcat_predict import batch_predict_kcat

def setup_logging(verbose=False):
    """设置日志"""
    level = logging.INFO if verbose else logging.WARNING
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def validate_csv(df):
    """验证CSV文件格式"""
    required_columns = ['Sequences', 'Smiles']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")
    
    # 检查空值
    empty_sequences = df['Sequences'].isna().sum()
    empty_smiles = df['Smiles'].isna().sum()
    
    if empty_sequences > 0:
        logging.warning(f"发现 {empty_sequences} 个空的蛋白质序列")
    
    if empty_smiles > 0:
        logging.warning(f"发现 {empty_smiles} 个空的SMILES")
    
    return df

def process_batch_prediction(df, batch_size=32, verbose=False):
    """批量预测处理"""
    logging.info(f"开始批量预测，样本数: {len(df)}, 批次大小: {batch_size}")
    
    # 准备数据
    sequences = df['Sequences'].fillna('').tolist()
    smiles_list = df['Smiles'].fillna('').tolist()
    
    # 批量预测
    start_time = time.time()
    try:
        results = batch_predict_kcat(
            sequences, 
            smiles_list, 
            batch_size=batch_size, 
            verbose=verbose
        )
        
        # 处理结果
        df_result = df.copy()
        df_result['Predicted_Log10_Kcat'] = results
        df_result['Predicted_Kcat'] = [10**r if r is not None else None for r in results]
        df_result['Prediction_Status'] = ['Success' if r is not None else 'Failed' for r in results]
        
        end_time = time.time()
        
        # 统计结果
        success_count = sum(1 for r in results if r is not None)
        failed_count = len(results) - success_count
        
        logging.info(f"预测完成! 耗时: {end_time - start_time:.2f}秒")
        logging.info(f"成功: {success_count}, 失败: {failed_count}")
        
        return df_result
        
    except Exception as e:
        logging.error(f"批量预测失败: {e}")
        raise

def main():
    parser = argparse.ArgumentParser(description='Kcat预测CSV批处理')
    parser.add_argument('-i', '--input', required=True, help='输入CSV文件')
    parser.add_argument('-o', '--output', required=True, help='输出CSV文件')
    parser.add_argument('-b', '--batch', type=int, default=32, help='批次大小')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    try:
        # 读取CSV文件
        logging.info(f"读取输入文件: {args.input}")
        df = pd.read_csv(args.input)
        logging.info(f"读取到 {len(df)} 行数据")
        
        # 验证格式
        df = validate_csv(df)
        
        # 批量预测
        df_result = process_batch_prediction(df, args.batch, args.verbose)
        
        # 保存结果
        logging.info(f"保存结果到: {args.output}")
        df_result.to_csv(args.output, index=False)
        
        print(f"✅ 预测完成! 结果已保存到: {args.output}")
        
    except Exception as e:
        logging.error(f"处理失败: {e}")
        print(f"❌ 处理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF
    
    log_success "Python脚本创建完成"
}

# 执行预测
run_prediction() {
    log_info "开始执行Kcat预测..."
    log_info "输入文件: $INPUT_FILE"
    log_info "输出文件: $OUTPUT_FILE"
    log_info "批次大小: $BATCH_SIZE"
    
    # 切换到工作目录并激活conda环境
    cd "$WORK_DIR"
    
    # 构建命令
    CMD="conda run -n $KCAT_ENV python $PYTHON_SCRIPT -i \"$INPUT_FILE\" -o \"$OUTPUT_FILE\" -b $BATCH_SIZE"
    
    if [[ "$VERBOSE" == "true" ]]; then
        CMD="$CMD -v"
    fi
    
    log_info "执行命令: $CMD"
    
    # 执行预测
    if eval "$CMD"; then
        log_success "Kcat预测完成!"
        
        # 显示结果统计
        if [[ -f "$OUTPUT_FILE" ]]; then
            log_info "结果文件信息:"
            log_info "  文件路径: $OUTPUT_FILE"
            log_info "  文件大小: $(du -h "$OUTPUT_FILE" | cut -f1)"
            
            # 尝试显示结果统计
            if command -v python3 &> /dev/null; then
                python3 -c "
import pandas as pd
try:
    df = pd.read_csv('$OUTPUT_FILE')
    total = len(df)
    success = df['Prediction_Status'].value_counts().get('Success', 0)
    failed = df['Prediction_Status'].value_counts().get('Failed', 0)
    print(f'  总样本数: {total}')
    print(f'  成功预测: {success}')
    print(f'  预测失败: {failed}')
    print(f'  成功率: {success/total*100:.1f}%')
except Exception as e:
    print(f'  无法读取结果统计: {e}')
"
            fi
        fi
    else
        log_error "Kcat预测失败!"
        exit 1
    fi
}

# 清理临时文件
cleanup() {
    if [[ -f "$WORK_DIR/$PYTHON_SCRIPT" ]]; then
        rm -f "$WORK_DIR/$PYTHON_SCRIPT"
        log_info "清理临时文件完成"
    fi
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -i|--input)
                INPUT_FILE="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_FILE="$2"
                shift 2
                ;;
            -b|--batch)
                BATCH_SIZE="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置默认值
    BATCH_SIZE=${BATCH_SIZE:-32}
    VERBOSE=${VERBOSE:-false}
    
    # 转换为绝对路径
    if [[ -n "$INPUT_FILE" ]]; then
        INPUT_FILE=$(realpath "$INPUT_FILE")
    fi
    if [[ -n "$OUTPUT_FILE" ]]; then
        OUTPUT_FILE=$(realpath "$OUTPUT_FILE")
    fi
    
    # 显示脚本信息
    echo "🧬 Kcat预测批处理脚本 v1.0"
    echo "=================================="
    
    # 检查参数和环境
    check_arguments
    check_environment
    
    # 设置清理函数
    trap cleanup EXIT
    
    # 创建Python脚本并执行预测
    create_python_script
    run_prediction
    
    log_success "所有任务完成!"
}

# 执行主函数
main "$@"
