#!/bin/bash

# Kcat预测批处理脚本 - 精简版
set -e

# 配置
KCAT_ENV="kcat"
WORK_DIR="/usr/XML/日常工作/朱星学/kcat"

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -i) INPUT_FILE="$2"; shift 2 ;;
        -o) OUTPUT_FILE="$2"; shift 2 ;;
        *) echo "用法: $0 -i <input.csv> -o <output.csv>"; exit 1 ;;
    esac
done

# 检查参数
if [[ -z "$INPUT_FILE" || -z "$OUTPUT_FILE" ]]; then
    echo "用法: $0 -i <input.csv> -o <output.csv>"
    exit 1
fi

if [[ ! -f "$INPUT_FILE" ]]; then
    echo "错误: 输入文件不存在: $INPUT_FILE"
    exit 1
fi

# 转换为绝对路径
INPUT_FILE=$(realpath "$INPUT_FILE")
OUTPUT_FILE=$(realpath "$OUTPUT_FILE")

# 创建输出目录
mkdir -p "$(dirname "$OUTPUT_FILE")"

# 切换到工作目录
cd "$WORK_DIR"

# 执行预测
echo "🧬 开始Kcat预测..."
conda run -n "$KCAT_ENV" python -c "
import pandas as pd
import sys
from kcat_predict import batch_predict_kcat

# 读取CSV
df = pd.read_csv('$INPUT_FILE')

# 检查必需列
if 'Sequences' not in df.columns or 'Smiles' not in df.columns:
    print('错误: CSV文件必须包含 Sequences 和 Smiles 列')
    sys.exit(1)

# 批量预测
sequences = df['Sequences'].fillna('').tolist()
smiles_list = df['Smiles'].fillna('').tolist()

print(f'开始预测 {len(df)} 个样本...')
results = batch_predict_kcat(sequences, smiles_list, batch_size=32)

# 添加结果列
df['Predicted_Log10_Kcat'] = results
df['Predicted_Kcat'] = [10**r if r is not None else None for r in results]
df['Prediction_Status'] = ['Success' if r is not None else 'Failed' for r in results]

# 保存结果
df.to_csv('$OUTPUT_FILE', index=False)

# 统计
success = sum(1 for r in results if r is not None)
print(f'预测完成! 成功: {success}/{len(results)}')
"

echo "✅ 预测完成! 结果保存到: $OUTPUT_FILE"
