#!/usr/bin/env python3
"""
重置和重新下载Facebook官方MSA模型
"""

import os
import shutil
from pathlib import Path
import torch

def clear_existing_models():
    """清理现有的MSA模型缓存"""
    
    print("[CLEAN] 清理现有MSA模型缓存...")
    
    # 清理torch hub缓存
    torch_cache = Path.home() / ".cache" / "torch" / "hub" / "checkpoints"
    msa_files = [
        "esm_msa1b_t12_100M_UR50S.pt",
        "esm_msa1b_t12_100M_UR50S-contact-regression.pt"
    ]
    
    for file in msa_files:
        file_path = torch_cache / file
        if file_path.exists():
            print(f"  删除: {file_path}")
            file_path.unlink()
        else:
            print(f"  未找到: {file_path}")
    
    # 清理HuggingFace缓存中的katielink模型
    hf_cache = Path.home() / ".cache" / "huggingface" / "hub"
    katielink_dirs = [
        "models--katielink--esm_msa1b_t12_100M_UR50S",
        "models--facebook--esm_msa1b_t12_100M_UR50S"
    ]
    
    for dir_name in katielink_dirs:
        dir_path = hf_cache / dir_name
        if dir_path.exists():
            print(f"  删除目录: {dir_path}")
            shutil.rmtree(dir_path)
        else:
            print(f"  未找到目录: {dir_path}")
    
    print("✅ 缓存清理完成")

def download_facebook_msa_model():
    """重新下载Facebook官方MSA模型"""
    
    print("\n[DOWNLOAD] 重新下载Facebook官方MSA模型...")
    
    try:
        import esm
        print("使用fair-esm库下载模型...")
        
        # 这会自动下载Facebook官方模型
        model, alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()
        
        print("[SUCCESS] Facebook MSA模型下载成功!")
        print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 检查下载的文件
        torch_cache = Path.home() / ".cache" / "torch" / "hub" / "checkpoints"
        msa_file = torch_cache / "esm_msa1b_t12_100M_UR50S.pt"
        if msa_file.exists():
            size_mb = msa_file.stat().st_size / (1024 * 1024)
            print(f"模型文件大小: {size_mb:.1f} MB")
            print(f"模型文件位置: {msa_file}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 下载失败: {e}")
        return False

def test_msa_model():
    """测试MSA模型是否正常工作"""
    
    print("\n🧪 测试MSA模型...")
    
    try:
        from feature_extractor import ProteinFeatureExtractor
        
        print("初始化特征提取器...")
        extractor = ProteinFeatureExtractor()
        
        print("测试序列特征提取...")
        sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        features = extractor.extract_features(sequence)
        
        print("✅ MSA模型测试成功!")
        
        # 检查MSA特征
        msa_features = features["msa"]
        if "query_representation" in msa_features:
            query_repr = msa_features["query_representation"]
            print(f"MSA特征形状: {query_repr.shape}")
            print(f"MSA特征维度: {query_repr.shape[-1]}")
            
            if query_repr.shape[-1] == 768:
                print("✅ 使用的是768维MSA Transformer特征")
            else:
                print(f"⚠️  使用的是{query_repr.shape[-1]}维传统MSA特征")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_model_info():
    """显示模型信息"""
    
    print("\n[INFO] 模型信息:")
    print("=" * 50)
    
    # ESM-2模型
    esm2_dir = Path.home() / ".cache" / "huggingface" / "hub" / "models--facebook--esm2_t33_650M_UR50D"
    if esm2_dir.exists():
        print("[PASS] ESM-2模型: 已安装")
    else:
        print("[FAIL] ESM-2模型: 未安装")
    
    # MSA模型 (torch hub)
    torch_cache = Path.home() / ".cache" / "torch" / "hub" / "checkpoints"
    msa_file = torch_cache / "esm_msa1b_t12_100M_UR50S.pt"
    if msa_file.exists():
        size_mb = msa_file.stat().st_size / (1024 * 1024)
        print(f"[PASS] MSA模型: 已安装 ({size_mb:.1f} MB)")
        print(f"   位置: {msa_file}")
    else:
        print("[FAIL] MSA模型: 未安装")
    
    # HuggingFace缓存
    hf_cache = Path.home() / ".cache" / "huggingface" / "hub"
    hf_models = list(hf_cache.glob("models--*esm*"))
    if hf_models:
        print(f"\n📁 HuggingFace缓存中的ESM模型:")
        for model_dir in hf_models:
            print(f"   {model_dir.name}")

def main():
    """主函数"""
    
    print("Facebook MSA模型重置工具")
    print("=" * 50)
    
    # 显示当前状态
    show_model_info()
    
    # 询问用户是否继续
    print("\n⚠️  这将删除现有的MSA模型缓存并重新下载Facebook官方模型")
    response = input("是否继续? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes', '是']:
        print("操作已取消")
        return
    
    # 清理现有模型
    clear_existing_models()
    
    # 重新下载Facebook模型
    if download_facebook_msa_model():
        print("\n🎉 Facebook MSA模型重新下载完成!")
        
        # 测试模型
        if test_msa_model():
            print("\n✅ 所有测试通过，模型已准备就绪!")
        else:
            print("\n⚠️  模型下载成功但测试失败，请检查配置")
    else:
        print("\n❌ 模型下载失败")
    
    # 显示最终状态
    print("\n" + "=" * 50)
    print("最终状态:")
    show_model_info()

if __name__ == "__main__":
    main()
