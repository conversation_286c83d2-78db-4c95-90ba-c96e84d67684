# ESM-2 蛋白质序列特征提取演示

## 📋 概述

本演示程序 `esm2_demo.py` 成功实现了以下功能：

1. **生成100长度的蛋白质序列**
2. **使用ESM-2模型进行特征映射**
3. **获得完整的(100, 1280)映射矩阵**
4. **提供详细的分析和可视化**

## 🧬 生成的蛋白质序列

```
序列长度: 100 氨基酸
序列: IVRNEEATNQAYTEEEGLKGNDGIKSELNANEAVWSGDQKDLAVGPGLLEYSVVNVCEAHIGSIGLDSAYSEASQRSAIDTPHAGHRPTLDRRMSLLKAD
```

**特点:**
- 使用天然氨基酸频率分布生成
- 包含20种标准氨基酸
- 符合真实蛋白质序列的统计特性

## 🔬 ESM-2特征提取结果

### 核心特征矩阵
- **形状**: (100, 1280)
- **数据类型**: float32
- **内存大小**: 0.49 MB

### 特征统计
```
均值: -0.001866
标准差: 0.298095
最小值: -8.438082
最大值: 1.615528
L2范数: 106.651794
```

### 模型信息
- **模型**: ESM-2 (esm2_t33_650M_UR50D)
- **参数量**: 651,043,254
- **运行设备**: CUDA GPU
- **特征层**: 第33层 (最后一层)

## 📊 输出文件详情

### 1. 核心数据文件
- `esm2_feature_matrix.npy` - 主要特征矩阵 (100×1280)
- `esm2_full_features.pt` - 完整PyTorch特征文件
- `sequence_info.txt` - 序列和统计信息

### 2. 分析文件
- `position_statistics.csv` - 每个位置的特征统计
- `feature_dimension_statistics.csv` - 每个特征维度的统计

### 3. 可视化文件
- `esm2_feature_heatmap.png` - 特征矩阵热图
- `position_feature_norms.png` - 每个位置的特征范数
- `feature_dimension_stats.png` - 特征维度分布
- `amino_acid_feature_distribution.png` - 氨基酸类型特征分布

## 🎯 特征矩阵详细信息

### 维度解释
- **第一维 (100)**: 蛋白质序列的每个氨基酸位置
- **第二维 (1280)**: ESM-2模型的特征维度

### 位置特征示例
```
位置 1 (I): 维度=1280, 范数=10.3568
位置 2 (V): 维度=1280, 范数=10.5478
位置 3 (R): 维度=1280, 范数=10.6359
位置 4 (N): 维度=1280, 范数=10.6401
位置 5 (E): 维度=1280, 范数=10.6134
```

### 特征维度分布
- **均值范围**: [-7.9949, 1.3677]
- **标准差范围**: [0.0505, 0.4461]

## 💡 使用示例

### 1. 加载特征矩阵
```python
import numpy as np
feature_matrix = np.load("esm2_demo_output/esm2_feature_matrix.npy")
print(f"形状: {feature_matrix.shape}")  # (100, 1280)
```

### 2. 获取特定位置的特征
```python
# 获取第5个位置的1280维特征向量
position_5_features = feature_matrix[4, :]  # 索引从0开始
print(f"第5个位置特征维度: {position_5_features.shape}")  # (1280,)
```

### 3. 获取特定特征维度
```python
# 获取第100个特征维度在所有位置的值
dimension_100_values = feature_matrix[:, 99]  # 索引从0开始
print(f"第100维特征在所有位置: {dimension_100_values.shape}")  # (100,)
```

### 4. 计算位置相似性
```python
# 计算位置1和位置2的余弦相似性
pos1 = feature_matrix[0, :]
pos2 = feature_matrix[1, :]
similarity = np.dot(pos1, pos2) / (np.linalg.norm(pos1) * np.linalg.norm(pos2))
print(f"相似性: {similarity:.4f}")
```

## 🔍 验证结果

运行 `verify_esm2_results.py` 可以验证所有生成的文件：

```bash
conda activate attention_fusion
python verify_esm2_results.py
```

验证内容包括：
- ✅ 特征矩阵形状和数据类型
- ✅ 序列信息完整性
- ✅ 统计文件正确性
- ✅ 可视化文件生成
- ✅ 特征质量分析

## 🚀 技术特点

### 1. 完整的ESM-2特征
- 使用ESM-2最新的650M参数模型
- 提取最后一层(第33层)的表示
- 包含丰富的蛋白质语义信息

### 2. 高质量的特征矩阵
- 每个位置都有1280维的密集特征向量
- 特征包含氨基酸的上下文信息
- 适用于各种下游任务

### 3. 完整的分析流程
- 从序列生成到特征提取
- 详细的统计分析
- 丰富的可视化展示

## 📈 应用场景

这个100×1280的ESM-2特征矩阵可以用于：

1. **蛋白质分类**: 使用特征进行功能分类
2. **结构预测**: 预测蛋白质二级/三级结构
3. **功能预测**: 预测蛋白质功能位点
4. **进化分析**: 分析序列进化关系
5. **药物设计**: 用于药物-蛋白质相互作用预测

## 🎉 总结

成功完成了ESM-2演示的所有目标：

- ✅ **生成**: 100长度的真实蛋白质序列
- ✅ **映射**: 使用ESM-2进行特征提取
- ✅ **矩阵**: 获得完整的(100, 1280)映射矩阵
- ✅ **分析**: 提供详细的特征分析
- ✅ **可视化**: 生成丰富的图表展示
- ✅ **验证**: 确保所有结果的正确性

这个演示为您提供了一个完整的ESM-2特征提取工作流程，可以作为更复杂蛋白质分析任务的基础！
