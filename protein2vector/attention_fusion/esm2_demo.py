#!/usr/bin/env python3
"""
ESM-2演示文件
生成100长度的蛋白质序列，使用ESM-2进行特征映射，获得完整的映射矩阵
"""

import torch
import numpy as np
import random
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pandas as pd
from typing import Dict, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_protein_sequence(length: int = 100) -> str:
    """
    生成指定长度的随机蛋白质序列

    Args:
        length: 序列长度

    Returns:
        蛋白质序列字符串
    """
    # 20种标准氨基酸，按自然频率加权
    amino_acids = "ACDEFGHIKLMNPQRSTVWY"

    # 氨基酸在天然蛋白质中的大致频率
    frequencies = np.array([
        0.082,  # A - 丙氨酸
        0.015,  # C - 半胱氨酸
        0.054,  # D - 天冬氨酸
        0.062,  # E - 谷氨酸
        0.039,  # F - 苯丙氨酸
        0.068,  # G - 甘氨酸
        0.022,  # H - 组氨酸
        0.057,  # I - 异亮氨酸
        0.058,  # K - 赖氨酸
        0.095,  # L - 亮氨酸
        0.024,  # M - 蛋氨酸
        0.043,  # N - 天冬酰胺
        0.048,  # P - 脯氨酸
        0.039,  # Q - 谷氨酰胺
        0.052,  # R - 精氨酸
        0.068,  # S - 丝氨酸
        0.058,  # T - 苏氨酸
        0.067,  # V - 缬氨酸
        0.013,  # W - 色氨酸
        0.032   # Y - 酪氨酸
    ])

    # 归一化频率确保和为1
    frequencies = frequencies / frequencies.sum()

    # 根据频率生成序列
    sequence = ''.join(np.random.choice(list(amino_acids), size=length, p=frequencies))

    return sequence

def load_esm2_model(model_name: str = "esm2_t33_650M_UR50D") -> Tuple[object, object]:
    """
    加载ESM-2模型和tokenizer

    Args:
        model_name: 模型名称

    Returns:
        (model, tokenizer)
    """
    try:
        import esm
        logger.info(f"Loading ESM-2 model: {model_name}")

        # 加载预训练模型
        model, alphabet = esm.pretrained.esm2_t33_650M_UR50D()
        batch_converter = alphabet.get_batch_converter()

        # 设置为评估模式
        model.eval()

        # 检查GPU可用性
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(device)

        logger.info(f"Model loaded successfully on {device}")
        logger.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

        return model, batch_converter, device

    except ImportError:
        logger.error("ESM library not found. Please install: pip install fair-esm")
        raise
    except Exception as e:
        logger.error(f"Failed to load ESM-2 model: {e}")
        raise

def extract_esm2_features(sequence: str, model, batch_converter, device) -> Dict[str, torch.Tensor]:
    """
    使用ESM-2提取蛋白质序列特征

    Args:
        sequence: 蛋白质序列
        model: ESM-2模型
        batch_converter: 批处理转换器
        device: 计算设备

    Returns:
        特征字典
    """
    logger.info(f"Extracting ESM-2 features for sequence of length {len(sequence)}")

    # 准备输入数据
    data = [("protein_seq", sequence)]
    batch_labels, batch_strs, batch_tokens = batch_converter(data)
    batch_tokens = batch_tokens.to(device)

    logger.info(f"Input tokens shape: {batch_tokens.shape}")

    # 提取特征
    with torch.no_grad():
        results = model(batch_tokens, repr_layers=[33], return_contacts=True)

    # 获取各种特征
    features = {
        "sequence": sequence,
        "tokens": batch_tokens,
        "representations": results["representations"][33],  # 最后一层的表示
        "logits": results["logits"],  # 预测logits
        "contacts": results["contacts"],  # 接触预测
        "attentions": results["attentions"] if "attentions" in results else None
    }

    # 移除特殊token (CLS和SEP)
    seq_len = len(sequence)
    features["sequence_representations"] = features["representations"][0, 1:seq_len+1, :]

    logger.info(f"Extracted features:")
    logger.info(f"  - Full representations: {features['representations'].shape}")
    logger.info(f"  - Sequence representations: {features['sequence_representations'].shape}")
    logger.info(f"  - Logits: {features['logits'].shape}")
    logger.info(f"  - Contacts: {features['contacts'].shape}")

    return features

def analyze_feature_matrix(features: Dict[str, torch.Tensor], sequence: str) -> Dict[str, any]:
    """
    分析ESM-2特征矩阵

    Args:
        features: ESM-2特征字典
        sequence: 原始序列

    Returns:
        分析结果
    """
    logger.info("Analyzing ESM-2 feature matrix...")

    seq_repr = features["sequence_representations"].cpu().numpy()
    seq_len, feature_dim = seq_repr.shape

    analysis = {
        "sequence": sequence,
        "sequence_length": seq_len,
        "feature_dimension": feature_dim,
        "feature_matrix": seq_repr,
        "feature_stats": {
            "mean": np.mean(seq_repr),
            "std": np.std(seq_repr),
            "min": np.min(seq_repr),
            "max": np.max(seq_repr),
            "norm": np.linalg.norm(seq_repr)
        }
    }

    # 计算每个位置的特征统计
    position_stats = []
    for i in range(seq_len):
        pos_features = seq_repr[i, :]
        position_stats.append({
            "position": i + 1,
            "amino_acid": sequence[i],
            "mean": np.mean(pos_features),
            "std": np.std(pos_features),
            "norm": np.linalg.norm(pos_features)
        })

    analysis["position_stats"] = position_stats

    # 计算特征维度统计
    feature_stats = []
    for j in range(feature_dim):
        dim_features = seq_repr[:, j]
        feature_stats.append({
            "dimension": j + 1,
            "mean": np.mean(dim_features),
            "std": np.std(dim_features),
            "range": np.max(dim_features) - np.min(dim_features)
        })

    analysis["feature_dimension_stats"] = feature_stats

    logger.info(f"Analysis completed:")
    logger.info(f"  - Matrix shape: {seq_repr.shape}")
    logger.info(f"  - Overall mean: {analysis['feature_stats']['mean']:.4f}")
    logger.info(f"  - Overall std: {analysis['feature_stats']['std']:.4f}")
    logger.info(f"  - Feature range: [{analysis['feature_stats']['min']:.4f}, {analysis['feature_stats']['max']:.4f}]")

    return analysis

def visualize_feature_matrix(analysis: Dict[str, any], output_dir: Path):
    """
    可视化ESM-2特征矩阵

    Args:
        analysis: 分析结果
        output_dir: 输出目录
    """
    logger.info("Creating visualizations...")

    feature_matrix = analysis["feature_matrix"]
    sequence = analysis["sequence"]
    seq_len, feature_dim = feature_matrix.shape

    # 创建输出目录
    output_dir.mkdir(exist_ok=True)

    # 1. 特征矩阵热图
    plt.figure(figsize=(15, 8))

    # 只显示前100个特征维度（如果超过100）
    display_dims = min(100, feature_dim)
    display_matrix = feature_matrix[:, :display_dims]

    sns.heatmap(display_matrix.T,
                cmap='RdBu_r',
                center=0,
                xticklabels=[f"{sequence[i]}{i+1}" for i in range(seq_len)],
                yticklabels=[f"Dim{j+1}" for j in range(display_dims)],
                cbar_kws={'label': 'Feature Value'})

    plt.title(f'ESM-2 Feature Matrix Heatmap\n(Sequence Length: {seq_len}, Feature Dimensions: {display_dims}/{feature_dim})')
    plt.xlabel('Amino Acid Position')
    plt.ylabel('Feature Dimension')
    plt.xticks(rotation=90)
    plt.tight_layout()
    plt.savefig(output_dir / "esm2_feature_heatmap.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 每个位置的特征范数
    position_norms = [stats["norm"] for stats in analysis["position_stats"]]
    amino_acids = [stats["amino_acid"] for stats in analysis["position_stats"]]

    plt.figure(figsize=(15, 6))
    bars = plt.bar(range(seq_len), position_norms, color='skyblue', alpha=0.7)

    # 为每个bar添加氨基酸标签
    for i, (bar, aa) in enumerate(zip(bars, amino_acids)):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                aa, ha='center', va='bottom', fontsize=8)

    plt.title('Feature Vector Norm by Position')
    plt.xlabel('Amino Acid Position')
    plt.ylabel('L2 Norm')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_dir / "position_feature_norms.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 特征维度分布
    feature_means = [stats["mean"] for stats in analysis["feature_dimension_stats"]]
    feature_stds = [stats["std"] for stats in analysis["feature_dimension_stats"]]

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

    # 特征均值分布
    ax1.plot(feature_means, color='blue', alpha=0.7)
    ax1.set_title('Feature Dimension Means')
    ax1.set_xlabel('Feature Dimension')
    ax1.set_ylabel('Mean Value')
    ax1.grid(True, alpha=0.3)

    # 特征标准差分布
    ax2.plot(feature_stds, color='red', alpha=0.7)
    ax2.set_title('Feature Dimension Standard Deviations')
    ax2.set_xlabel('Feature Dimension')
    ax2.set_ylabel('Standard Deviation')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / "feature_dimension_stats.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 4. 氨基酸类型的特征分布
    aa_types = {}
    for stats in analysis["position_stats"]:
        aa = stats["amino_acid"]
        if aa not in aa_types:
            aa_types[aa] = []
        aa_types[aa].append(stats["norm"])

    plt.figure(figsize=(12, 6))
    aa_names = list(aa_types.keys())
    aa_means = [np.mean(aa_types[aa]) for aa in aa_names]
    aa_stds = [np.std(aa_types[aa]) for aa in aa_names]

    bars = plt.bar(aa_names, aa_means, yerr=aa_stds, capsize=5,
                   color='lightgreen', alpha=0.7, edgecolor='black')

    plt.title('Average Feature Norm by Amino Acid Type')
    plt.xlabel('Amino Acid')
    plt.ylabel('Average L2 Norm')
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(output_dir / "amino_acid_feature_distribution.png", dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"Visualizations saved to {output_dir}")

def save_results(analysis: Dict[str, any], features: Dict[str, torch.Tensor], output_dir: Path):
    """
    保存分析结果

    Args:
        analysis: 分析结果
        features: ESM-2特征
        output_dir: 输出目录
    """
    logger.info("Saving results...")

    # 保存特征矩阵
    feature_matrix = analysis["feature_matrix"]
    np.save(output_dir / "esm2_feature_matrix.npy", feature_matrix)

    # 保存序列信息
    with open(output_dir / "sequence_info.txt", "w") as f:
        f.write(f"Generated Protein Sequence (Length: {len(analysis['sequence'])})\n")
        f.write("=" * 50 + "\n")
        f.write(f"Sequence: {analysis['sequence']}\n\n")
        f.write(f"ESM-2 Feature Matrix Shape: {feature_matrix.shape}\n")
        f.write(f"Feature Statistics:\n")
        f.write(f"  Mean: {analysis['feature_stats']['mean']:.6f}\n")
        f.write(f"  Std:  {analysis['feature_stats']['std']:.6f}\n")
        f.write(f"  Min:  {analysis['feature_stats']['min']:.6f}\n")
        f.write(f"  Max:  {analysis['feature_stats']['max']:.6f}\n")
        f.write(f"  Norm: {analysis['feature_stats']['norm']:.6f}\n")

    # 保存位置统计
    position_df = pd.DataFrame(analysis["position_stats"])
    position_df.to_csv(output_dir / "position_statistics.csv", index=False)

    # 保存特征维度统计
    feature_df = pd.DataFrame(analysis["feature_dimension_stats"])
    feature_df.to_csv(output_dir / "feature_dimension_statistics.csv", index=False)

    # 保存完整特征（可选，文件较大）
    torch.save({
        "sequence_representations": features["sequence_representations"],
        "full_representations": features["representations"],
        "logits": features["logits"],
        "contacts": features["contacts"]
    }, output_dir / "esm2_full_features.pt")

    logger.info(f"Results saved to {output_dir}")

def main():
    """主函数"""

    print("🧬 ESM-2 蛋白质序列特征提取演示")
    print("=" * 50)

    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)

    # 1. 生成100长度的蛋白质序列
    print("\n1. 生成蛋白质序列...")
    sequence = generate_protein_sequence(length=100)
    print(f"生成的序列 (长度: {len(sequence)}):")
    print(f"{sequence}")

    # 2. 加载ESM-2模型
    print("\n2. 加载ESM-2模型...")
    model, batch_converter, device = load_esm2_model()

    # 3. 提取特征
    print("\n3. 提取ESM-2特征...")
    features = extract_esm2_features(sequence, model, batch_converter, device)

    # 4. 分析特征矩阵
    print("\n4. 分析特征矩阵...")
    analysis = analyze_feature_matrix(features, sequence)

    # 5. 创建可视化
    print("\n5. 创建可视化...")
    output_dir = Path("esm2_demo_output")
    visualize_feature_matrix(analysis, output_dir)

    # 6. 保存结果
    print("\n6. 保存结果...")
    save_results(analysis, features, output_dir)

    # 7. 显示总结
    print("\n" + "=" * 50)
    print("📊 ESM-2特征提取完成！")
    print("=" * 50)
    print(f"序列长度: {analysis['sequence_length']}")
    print(f"特征维度: {analysis['feature_dimension']}")
    print(f"特征矩阵形状: {analysis['feature_matrix'].shape}")
    print(f"特征统计:")
    print(f"  均值: {analysis['feature_stats']['mean']:.6f}")
    print(f"  标准差: {analysis['feature_stats']['std']:.6f}")
    print(f"  范围: [{analysis['feature_stats']['min']:.6f}, {analysis['feature_stats']['max']:.6f}]")

    print(f"\n📁 输出文件:")
    print(f"  - 特征矩阵: {output_dir}/esm2_feature_matrix.npy")
    print(f"  - 序列信息: {output_dir}/sequence_info.txt")
    print(f"  - 位置统计: {output_dir}/position_statistics.csv")
    print(f"  - 特征统计: {output_dir}/feature_dimension_statistics.csv")
    print(f"  - 完整特征: {output_dir}/esm2_full_features.pt")
    print(f"  - 可视化图片: {output_dir}/*.png")

    print(f"\n🎉 演示完成！您现在拥有了100长度蛋白质的完整ESM-2映射矩阵！")

if __name__ == "__main__":
    main()