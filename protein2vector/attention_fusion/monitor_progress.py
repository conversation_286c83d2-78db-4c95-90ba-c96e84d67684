#!/usr/bin/env python3
"""
监控Km_Data处理进度
"""

import json
import os
import time
from datetime import datetime
import pandas as pd

def monitor_progress():
    """监控处理进度"""
    
    output_file = "../20250623_Data/Km_Data_fusion_vector.csv"
    progress_file = "../20250623_Data/processing_progress.json"
    total_sequences = 26626
    
    print("🔍 Km_Data处理进度监控")
    print("=" * 50)
    
    while True:
        try:
            # 检查输出文件
            if os.path.exists(output_file):
                # 计算已处理的序列数
                with open(output_file, 'r') as f:
                    processed_count = sum(1 for line in f) - 1  # 减去标题行
                
                # 读取进度文件
                progress_info = {}
                if os.path.exists(progress_file):
                    with open(progress_file, 'r') as f:
                        progress_info = json.load(f)
                
                # 计算进度
                progress_percent = (processed_count / total_sequences) * 100
                remaining = total_sequences - processed_count
                
                # 估算剩余时间
                if processed_count > 0:
                    # 假设处理速度约为每秒4个序列
                    estimated_speed = 4  # 序列/秒
                    remaining_time_seconds = remaining / estimated_speed
                    remaining_hours = remaining_time_seconds / 3600
                    remaining_minutes = (remaining_time_seconds % 3600) / 60
                else:
                    remaining_hours = 0
                    remaining_minutes = 0
                
                # 显示进度
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"\n[TIME] {current_time}")
                print(f"[PROGRESS] 处理进度: {processed_count:,}/{total_sequences:,} ({progress_percent:.1f}%)")
                print(f"[BAR] 进度条: {'█' * int(progress_percent/2):<50} {progress_percent:.1f}%")
                print(f"[REMAIN] 剩余序列: {remaining:,}")
                print(f"[ETA] 预计剩余时间: {int(remaining_hours)}小时 {int(remaining_minutes)}分钟")
                
                if progress_info:
                    last_update = progress_info.get('last_update', 'Unknown')
                    print(f"[UPDATE] 最后更新: {last_update}")
                
                # 检查是否完成
                if processed_count >= total_sequences:
                    print("\n[SUCCESS] 处理完成!")
                    break
                    
            else:
                print(f"[WAIT] 等待处理开始... (输出文件不存在)")
            
            # 等待30秒后再次检查
            time.sleep(30)
            
        except KeyboardInterrupt:
            print("\n[STOP] 监控已停止")
            break
        except Exception as e:
            print(f"[ERROR] 监控错误: {e}")
            time.sleep(30)

def show_current_status():
    """显示当前状态"""
    
    output_file = "../20250623_Data/Km_Data_fusion_vector.csv"
    progress_file = "../20250623_Data/processing_progress.json"
    total_sequences = 26626
    
    print("📊 当前处理状态")
    print("=" * 30)
    
    # 检查文件
    if os.path.exists(output_file):
        # 读取文件行数
        with open(output_file, 'r') as f:
            processed_count = sum(1 for line in f) - 1
        
        progress_percent = (processed_count / total_sequences) * 100
        
        print(f"[PASS] 输出文件存在: {output_file}")
        print(f"[PROGRESS] 已处理序列: {processed_count:,}/{total_sequences:,}")
        print(f"[PERCENT] 完成度: {progress_percent:.1f}%")
        
        # 检查最近几行
        try:
            df = pd.read_csv(output_file)
            print(f"[COLUMNS] 文件列名: {list(df.columns)}")
            print(f"[LAST] 最后5个ID: {df['ID'].tail().tolist()}")
            
            # 检查成功率
            success_count = df['success'].sum()
            success_rate = (success_count / len(df)) * 100
            print(f"[SUCCESS_RATE] 成功率: {success_count}/{len(df)} ({success_rate:.1f}%)")
            
        except Exception as e:
            print(f"[WARNING] 读取文件详情失败: {e}")
    else:
        print(f"[ERROR] 输出文件不存在: {output_file}")
    
    # 检查进度文件
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r') as f:
                progress_info = json.load(f)
            print(f"📝 进度文件信息:")
            for key, value in progress_info.items():
                print(f"   {key}: {value}")
        except Exception as e:
            print(f"⚠️ 读取进度文件失败: {e}")
    else:
        print(f"❌ 进度文件不存在: {progress_file}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "status":
        show_current_status()
    else:
        monitor_progress()

if __name__ == "__main__":
    main()
