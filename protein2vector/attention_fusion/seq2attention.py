#!/usr/bin/env python3
"""
seq2attention.py - 蛋白质序列到attention融合特征向量的转换模块

该模块提供seq2attention函数，可以将蛋白质序列转换为512维的attention融合特征向量。
基于ESM-2和MSA Transformer的attention fusion方法。

使用示例:
    from seq2attention import seq2attention
    
    sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
    
    vector = seq2attention(sequence)
    print(f"特征向量维度: {len(vector)}")
    print(f"特征向量: {vector[:5]}...")  # 显示前5个值
"""

import numpy as np
import torch
import logging
from typing import Union, Optional, List
import warnings

# 导入attention_fusion模块
try:
    from feature_extractor import ProteinFeatureExtractor
    from attention_fusion_model import AttentionFusionModel
except ImportError as e:
    raise ImportError(f"无法导入attention_fusion模块: {e}. 请确保在正确的环境中运行此脚本。")

# 设置日志
logging.basicConfig(level=logging.WARNING)  # 默认只显示警告和错误
logger = logging.getLogger(__name__)

# 全局变量，用于缓存模型
_extractor = None
_model = None
_device = None

def _initialize_models():
    """初始化attention fusion模型（仅在首次调用时执行）"""
    global _extractor, _model, _device
    
    if _extractor is not None and _model is not None:
        return  # 模型已经初始化
    
    logger.info("初始化attention fusion模型...")
    
    # 设置设备
    _device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {_device}")
    
    try:
        # 初始化特征提取器
        _extractor = ProteinFeatureExtractor(
            esm_model_name="facebook/esm2_t33_650M_UR50D",
            msa_model_name="facebook/esm_msa1b_t12_100M_UR50S"
        )
        
        # 初始化融合模型
        _model = AttentionFusionModel(
            esm_dim=1280, 
            msa_dim=768,
            hidden_dim=512, 
            num_blocks=2, 
            num_heads=4
        )
        _model.to(_device)
        _model.eval()
        
        logger.info("[SUCCESS] Attention fusion模型初始化完成")
        
    except Exception as e:
        logger.error(f"模型初始化失败: {e}")
        raise RuntimeError(f"无法初始化attention fusion模型: {e}")

def _validate_sequence(sequence: str) -> str:
    """验证和清理蛋白质序列"""
    if not isinstance(sequence, str):
        raise TypeError("序列必须是字符串类型")
    
    # 清理序列
    sequence = sequence.strip().upper()
    
    # 移除非氨基酸字符
    valid_amino_acids = set('ACDEFGHIKLMNPQRSTVWY')
    cleaned_sequence = ''.join(c for c in sequence if c in valid_amino_acids)
    
    if not cleaned_sequence:
        raise ValueError("序列不包含有效的氨基酸")
    
    if len(cleaned_sequence) < 8:
        raise ValueError("序列长度太短（至少需要8个氨基酸）")
    
    if len(cleaned_sequence) > 1024:
        logger.warning(f"序列长度 {len(cleaned_sequence)} 超过1024，将截取前1024个氨基酸")
        cleaned_sequence = cleaned_sequence[:1024]
    
    return cleaned_sequence

def seq2attention(sequence: str, 
                 return_numpy: bool = True,
                 verbose: bool = False) -> Union[np.ndarray, torch.Tensor]:
    """
    将蛋白质序列转换为attention融合特征向量
    
    Args:
        sequence (str): 蛋白质序列（单字母氨基酸代码）
        return_numpy (bool): 是否返回numpy数组，默认True。如果False则返回torch.Tensor
        verbose (bool): 是否显示详细日志信息，默认False
        
    Returns:
        Union[np.ndarray, torch.Tensor]: 512维的attention融合特征向量
        
    Raises:
        TypeError: 如果sequence不是字符串类型
        ValueError: 如果序列无效或太短
        RuntimeError: 如果模型初始化或特征提取失败
        
    Example:
        >>> from seq2attention import seq2attention
        >>> sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
        >>> vector = seq2attention(sequence)
        >>> print(f"特征向量维度: {len(vector)}")
        特征向量维度: 512
    """
    
    # 设置日志级别
    if verbose:
        logging.getLogger(__name__).setLevel(logging.INFO)
    else:
        logging.getLogger(__name__).setLevel(logging.WARNING)
    
    try:
        # 验证序列
        cleaned_sequence = _validate_sequence(sequence)
        
        # 初始化模型（如果尚未初始化）
        _initialize_models()
        
        # 提取attention融合特征
        with torch.no_grad():
            # 提取融合特征
            results = _model.extract_and_fuse(cleaned_sequence, _extractor)
            
            # 获取融合特征 (batch_size, seq_len, hidden_dim)
            fused_features = results["fused_features"]  # [1, seq_len, 512]
            
            # 计算序列级表示 (平均池化) - architectural特征
            sequence_representation = fused_features.mean(dim=1).squeeze(0)  # [512]
            
            # 根据返回类型转换
            if return_numpy:
                feature_vector = sequence_representation.detach().cpu().numpy()
            else:
                feature_vector = sequence_representation.detach().cpu()
            
            if verbose:
                logger.info(f"[SUCCESS] 成功提取特征向量，维度: {feature_vector.shape}")
            
            return feature_vector
            
    except Exception as e:
        logger.error(f"特征提取失败: {e}")
        raise RuntimeError(f"无法提取序列的attention融合特征: {e}")

def batch_seq2attention(sequences: List[str], 
                       return_numpy: bool = True,
                       verbose: bool = False) -> Union[List[np.ndarray], List[torch.Tensor]]:
    """
    批量将蛋白质序列转换为attention融合特征向量
    
    Args:
        sequences (List[str]): 蛋白质序列列表
        return_numpy (bool): 是否返回numpy数组，默认True
        verbose (bool): 是否显示详细日志信息，默认False
        
    Returns:
        Union[List[np.ndarray], List[torch.Tensor]]: 特征向量列表
        
    Example:
        >>> sequences = ["ACDEFGHIKLMNPQRSTVWY", "MPIRVPDELPAVNFLREENVF"]
        >>> vectors = batch_seq2attention(sequences)
        >>> print(f"处理了 {len(vectors)} 个序列")
    """
    
    if not isinstance(sequences, list):
        raise TypeError("sequences必须是列表类型")
    
    if not sequences:
        raise ValueError("序列列表不能为空")
    
    results = []
    failed_indices = []
    
    for i, sequence in enumerate(sequences):
        try:
            vector = seq2attention(sequence, return_numpy=return_numpy, verbose=verbose)
            results.append(vector)
        except Exception as e:
            if verbose:
                logger.warning(f"序列 {i} 处理失败: {e}")
            results.append(None)
            failed_indices.append(i)
    
    if failed_indices and verbose:
        logger.warning(f"共有 {len(failed_indices)} 个序列处理失败: {failed_indices}")
    
    return results

def get_model_info() -> dict:
    """
    获取模型信息
    
    Returns:
        dict: 包含模型配置信息的字典
    """
    return {
        "esm_model": "facebook/esm2_t33_650M_UR50D",
        "msa_model": "facebook/esm_msa1b_t12_100M_UR50S",
        "esm_dim": 1280,
        "msa_dim": 768,
        "hidden_dim": 512,
        "num_blocks": 2,
        "num_heads": 4,
        "output_dim": 512,
        "max_sequence_length": 1024,
        "min_sequence_length": 8
    }

# 示例和测试代码
if __name__ == "__main__":
    # 测试示例
    test_sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
    
    print("测试seq2attention函数...")
    print(f"输入序列长度: {len(test_sequence)}")
    
    try:
        # 测试单个序列
        vector = seq2attention(test_sequence, verbose=True)
        print(f"[SUCCESS] 成功提取特征向量")
        print(f"特征向量维度: {vector.shape}")
        print(f"数值范围: [{vector.min():.3f}, {vector.max():.3f}]")
        print(f"平均值: {vector.mean():.3f}")
        print(f"前5个值: {vector[:5]}")
        
        # 测试批量处理
        test_sequences = [test_sequence[:100], test_sequence[50:150]]
        vectors = batch_seq2attention(test_sequences, verbose=True)
        print(f"[SUCCESS] 批量处理成功，处理了 {len([v for v in vectors if v is not None])} 个序列")
        
        # 显示模型信息
        info = get_model_info()
        print(f"[INFO] 模型信息: {info}")
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
