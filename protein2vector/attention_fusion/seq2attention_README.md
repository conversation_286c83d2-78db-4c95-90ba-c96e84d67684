# seq2attention - 蛋白质序列到Attention融合特征转换模块

## 概述

`seq2attention.py` 是一个基于ESM-2和MSA Transformer的attention fusion方法的蛋白质序列特征提取模块。它可以将蛋白质序列转换为512维的attention融合特征向量，适用于各种蛋白质分析任务。

## 特性

- **高质量特征提取**: 基于ESM-2 (650M参数) 和MSA Transformer的attention fusion方法
- **简单易用**: 提供简洁的API接口，一行代码即可提取特征
- **批量处理**: 支持单个序列和批量序列处理
- **灵活输出**: 支持numpy数组和torch张量输出格式
- **模型缓存**: 自动缓存已加载的模型，避免重复初始化
- **错误处理**: 完善的输入验证和错误处理机制

## 安装要求

确保在`attention_fusion` conda环境中运行：

```bash
conda activate attention_fusion
```

## 基本使用

### 1. 单个序列处理

```python
from seq2attention import seq2attention

# 蛋白质序列
sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"

# 提取特征向量
vector = seq2attention(sequence)

print(f"特征向量维度: {vector.shape}")  # (512,)
print(f"数值范围: [{vector.min():.3f}, {vector.max():.3f}]")
```

### 2. 批量处理

```python
from seq2attention import batch_seq2attention

sequences = [
    "ACDEFGHIKLMNPQRSTVWY",
    "MKLLVLSLCFLAVFTVFMSISSHAKGFKQVDQAIDQINQKWGKGVTQIKVVQFQKGQKYVVYRVDTQAYHAHTATQKTVDGPSGKLWRDGRGAAQNIIPASTGAAKAVGKVIPELNGKLTGMAFRVPTPNVSVVDLTCRLEKPAKYDDIKKVVKQASEGPLKGILGYTEHQVVSSDFNSDTHSSTFDAGAGIALNDHFVKLISWYDNEFGYSNRVVDLMAHMASKE",
    "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
]

vectors = batch_seq2attention(sequences)
print(f"处理了 {len([v for v in vectors if v is not None])} 个序列")
```

### 3. DataFrame处理

```python
import pandas as pd
from seq2attention import seq2attention

# 读取包含蛋白质序列的DataFrame
df = pd.read_csv('protein_data.csv')  # 假设有'sequence'列

# 为每个序列提取特征
df['attention_vector'] = df['sequence'].apply(lambda seq: seq2attention(seq))

# 或者转换为列表格式保存
df['vector_str'] = df['attention_vector'].apply(lambda v: ','.join(map(str, v)))
```

### 4. 相似性分析

```python
import numpy as np
from scipy.spatial.distance import cosine
from seq2attention import seq2attention

# 两个蛋白质序列
seq1 = "ACDEFGHIKLMNPQRSTVWY"
seq2 = "ACDEFGHIKLMNPQRSTVWY"  # 相同序列

# 提取特征向量
vector1 = seq2attention(seq1)
vector2 = seq2attention(seq2)

# 计算相似性
cosine_similarity = 1 - cosine(vector1, vector2)
euclidean_distance = np.linalg.norm(vector1 - vector2)

print(f"余弦相似度: {cosine_similarity:.6f}")
print(f"欧几里得距离: {euclidean_distance:.6f}")
```

## API参考

### seq2attention(sequence, return_numpy=True, verbose=False)

将单个蛋白质序列转换为attention融合特征向量。

**参数:**
- `sequence` (str): 蛋白质序列（单字母氨基酸代码）
- `return_numpy` (bool): 是否返回numpy数组，默认True
- `verbose` (bool): 是否显示详细日志，默认False

**返回:**
- `numpy.ndarray` 或 `torch.Tensor`: 512维特征向量

**异常:**
- `TypeError`: 序列不是字符串类型
- `ValueError`: 序列无效或太短（<8个氨基酸）
- `RuntimeError`: 模型初始化或特征提取失败

### batch_seq2attention(sequences, return_numpy=True, verbose=False)

批量处理多个蛋白质序列。

**参数:**
- `sequences` (List[str]): 蛋白质序列列表
- `return_numpy` (bool): 是否返回numpy数组，默认True
- `verbose` (bool): 是否显示详细日志，默认False

**返回:**
- `List[numpy.ndarray]` 或 `List[torch.Tensor]`: 特征向量列表

### get_model_info()

获取模型配置信息。

**返回:**
- `dict`: 包含模型配置的字典

## 模型配置

- **ESM-2模型**: `facebook/esm2_t33_650M_UR50D` (1280维)
- **MSA模型**: `facebook/esm_msa1b_t12_100M_UR50S` (768维)
- **融合维度**: 512维
- **注意力块数**: 2
- **注意力头数**: 4
- **最大序列长度**: 1024个氨基酸
- **最小序列长度**: 8个氨基酸

## 注意事项

1. **首次调用**: 第一次调用函数时会自动下载和初始化模型，可能需要几分钟时间
2. **内存需求**: 模型较大，建议在有足够GPU内存的环境中运行
3. **序列长度**: 超过1024个氨基酸的序列会被自动截取
4. **有效氨基酸**: 只接受标准的20种氨基酸字母，其他字符会被自动过滤

## 性能优化

- 模型会在首次调用时加载并缓存，后续调用无需重新加载
- 批量处理比逐个处理更高效
- 在GPU环境中运行可显著提升速度

## 示例脚本

运行 `example_usage.py` 查看完整的使用示例：

```bash
python example_usage.py
```

## 故障排除

1. **导入错误**: 确保在正确的conda环境中运行
2. **CUDA错误**: 如果GPU内存不足，模型会自动使用CPU
3. **序列错误**: 检查序列是否包含有效的氨基酸字符
4. **模型加载失败**: 检查网络连接，确保可以下载预训练模型

## 技术支持

如有问题，请检查：
1. conda环境是否正确激活
2. 所需的依赖包是否已安装
3. 序列格式是否正确
4. 系统内存和GPU内存是否充足
