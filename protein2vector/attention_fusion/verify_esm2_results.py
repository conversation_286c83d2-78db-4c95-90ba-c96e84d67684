#!/usr/bin/env python3
"""
验证ESM-2演示结果
加载并验证生成的特征矩阵
"""

import numpy as np
import torch
import pandas as pd
from pathlib import Path

def verify_esm2_results():
    """验证ESM-2演示结果"""
    
    print("🔍 验证ESM-2演示结果")
    print("=" * 50)
    
    output_dir = Path("esm2_demo_output")
    
    # 1. 验证特征矩阵
    print("\n1. 验证特征矩阵...")
    feature_matrix = np.load(output_dir / "esm2_feature_matrix.npy")
    print(f"   特征矩阵形状: {feature_matrix.shape}")
    print(f"   数据类型: {feature_matrix.dtype}")
    print(f"   内存大小: {feature_matrix.nbytes / 1024 / 1024:.2f} MB")
    
    # 2. 验证序列信息
    print("\n2. 验证序列信息...")
    with open(output_dir / "sequence_info.txt", "r") as f:
        content = f.read()
        lines = content.strip().split('\n')
        sequence_line = [line for line in lines if line.startswith("Sequence:")][0]
        sequence = sequence_line.split("Sequence: ")[1]
        print(f"   序列长度: {len(sequence)}")
        print(f"   序列: {sequence[:50]}...")
    
    # 3. 验证位置统计
    print("\n3. 验证位置统计...")
    position_stats = pd.read_csv(output_dir / "position_statistics.csv")
    print(f"   位置统计行数: {len(position_stats)}")
    print(f"   列名: {list(position_stats.columns)}")
    print(f"   前5个位置:")
    print(position_stats.head().to_string(index=False))
    
    # 4. 验证特征维度统计
    print("\n4. 验证特征维度统计...")
    feature_stats = pd.read_csv(output_dir / "feature_dimension_statistics.csv")
    print(f"   特征维度统计行数: {len(feature_stats)}")
    print(f"   列名: {list(feature_stats.columns)}")
    
    # 5. 验证完整特征文件
    print("\n5. 验证完整特征文件...")
    full_features = torch.load(output_dir / "esm2_full_features.pt")
    print(f"   包含的特征:")
    for key, value in full_features.items():
        if isinstance(value, torch.Tensor):
            print(f"     {key}: {value.shape}")
        else:
            print(f"     {key}: {type(value)}")
    
    # 6. 验证可视化文件
    print("\n6. 验证可视化文件...")
    png_files = list(output_dir.glob("*.png"))
    print(f"   生成的图片数量: {len(png_files)}")
    for png_file in png_files:
        print(f"     - {png_file.name}")
    
    # 7. 特征矩阵详细分析
    print("\n7. 特征矩阵详细分析...")
    print(f"   形状验证: {feature_matrix.shape} == (100, 1280) ? {feature_matrix.shape == (100, 1280)}")
    print(f"   统计信息:")
    print(f"     均值: {np.mean(feature_matrix):.6f}")
    print(f"     标准差: {np.std(feature_matrix):.6f}")
    print(f"     最小值: {np.min(feature_matrix):.6f}")
    print(f"     最大值: {np.max(feature_matrix):.6f}")
    print(f"     范数: {np.linalg.norm(feature_matrix):.6f}")
    
    # 8. 验证每个位置的特征向量
    print("\n8. 验证位置特征向量...")
    for i in range(min(5, feature_matrix.shape[0])):
        pos_vector = feature_matrix[i, :]
        print(f"   位置 {i+1} ({sequence[i]}): 维度={len(pos_vector)}, 范数={np.linalg.norm(pos_vector):.4f}")
    
    # 9. 验证特征维度分布
    print("\n9. 验证特征维度分布...")
    feature_means = np.mean(feature_matrix, axis=0)
    feature_stds = np.std(feature_matrix, axis=0)
    print(f"   特征维度均值分布: [{np.min(feature_means):.4f}, {np.max(feature_means):.4f}]")
    print(f"   特征维度标准差分布: [{np.min(feature_stds):.4f}, {np.max(feature_stds):.4f}]")
    
    print("\n" + "=" * 50)
    print("✅ ESM-2演示结果验证完成！")
    print("=" * 50)
    print("📊 总结:")
    print(f"   - 成功生成100长度蛋白质序列")
    print(f"   - 成功提取1280维ESM-2特征")
    print(f"   - 特征矩阵形状: (100, 1280)")
    print(f"   - 生成了完整的分析和可视化结果")
    print(f"   - 所有文件都已正确保存")
    
    return {
        'sequence': sequence,
        'feature_matrix': feature_matrix,
        'position_stats': position_stats,
        'feature_stats': feature_stats,
        'full_features': full_features
    }

def demonstrate_feature_usage():
    """演示如何使用生成的特征"""
    
    print("\n" + "=" * 50)
    print("🚀 特征使用演示")
    print("=" * 50)
    
    # 加载特征矩阵
    feature_matrix = np.load("esm2_demo_output/esm2_feature_matrix.npy")
    
    print("💡 特征矩阵使用示例:")
    print(f"   1. 获取特定位置的特征向量:")
    print(f"      position_5_features = feature_matrix[4, :]  # 第5个位置")
    print(f"      形状: {feature_matrix[4, :].shape}")
    
    print(f"\n   2. 获取特定特征维度在所有位置的值:")
    print(f"      dimension_100_values = feature_matrix[:, 99]  # 第100个特征维度")
    print(f"      形状: {feature_matrix[:, 99].shape}")
    
    print(f"\n   3. 计算位置间的相似性:")
    pos1_features = feature_matrix[0, :]
    pos2_features = feature_matrix[1, :]
    similarity = np.dot(pos1_features, pos2_features) / (np.linalg.norm(pos1_features) * np.linalg.norm(pos2_features))
    print(f"      位置1和位置2的余弦相似性: {similarity:.4f}")
    
    print(f"\n   4. 特征降维示例 (PCA前2个主成分):")
    from sklearn.decomposition import PCA
    pca = PCA(n_components=2)
    reduced_features = pca.fit_transform(feature_matrix)
    print(f"      降维后形状: {reduced_features.shape}")
    print(f"      解释方差比: {pca.explained_variance_ratio_}")
    
    print(f"\n   5. 聚类分析示例:")
    from sklearn.cluster import KMeans
    kmeans = KMeans(n_clusters=5, random_state=42)
    clusters = kmeans.fit_predict(feature_matrix)
    print(f"      聚类结果: {len(set(clusters))} 个簇")
    print(f"      每个簇的大小: {np.bincount(clusters)}")

if __name__ == "__main__":
    # 验证结果
    results = verify_esm2_results()
    
    # 演示使用方法
    try:
        demonstrate_feature_usage()
    except ImportError:
        print("\n⚠️  sklearn未安装，跳过高级分析演示")
        print("   安装命令: pip install scikit-learn")
