#!/usr/bin/env python3
"""
演示attention_fusion的直接使用方式
无需训练，直接提取融合特征
"""

import torch
import numpy as np
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel

def direct_feature_extraction_demo():
    """演示直接特征提取"""
    
    print("Attention Fusion 直接使用演示")
    print("=" * 50)
    
    # 初始化模型（无需训练）
    print("1. 初始化模型...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    extractor = ProteinFeatureExtractor()
    fusion_model = AttentionFusionModel(
        esm_dim=1280, 
        msa_dim=768,  # 使用Facebook MSA的768维
        hidden_dim=512, 
        num_blocks=2, 
        num_heads=4
    )
    fusion_model.to(device)
    
    print(f"模型初始化完成 (设备: {device})")
    print(f"   模型参数量: {sum(p.numel() for p in fusion_model.parameters()):,}")
    
    # 测试蛋白质序列
    test_sequences = [
        {
            "name": "短肽",
            "sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "description": "65个氨基酸的短肽"
        },
        {
            "name": "中等蛋白质",
            "sequence": "MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL",
            "description": "609个氨基酸的蛋白质"
        }
    ]
    
    print("\n2. 直接特征提取演示...")
    
    for i, seq_info in enumerate(test_sequences, 1):
        print(f"\n--- 序列 {i}: {seq_info['name']} ---")
        print(f"长度: {len(seq_info['sequence'])} 氨基酸")
        print(f"描述: {seq_info['description']}")
        
        try:
            # 直接提取融合特征
            results = fusion_model.extract_and_fuse(seq_info['sequence'], extractor)
            
            print("特征提取成功!")
            print(f"   融合特征形状: {results['fused_features'].shape}")
            print(f"   增强ESM特征形状: {results['enhanced_esm_features'].shape}")
            print(f"   增强MSA特征形状: {results['enhanced_msa_features'].shape}")
            print(f"   注意力层数: {len(results['attention_weights'])}")
            
            # 分析特征统计
            fused_features = results['fused_features'].cpu().numpy()
            print(f"   特征统计:")
            print(f"     均值: {np.mean(fused_features):.4f}")
            print(f"     标准差: {np.std(fused_features):.4f}")
            print(f"     最小值: {np.min(fused_features):.4f}")
            print(f"     最大值: {np.max(fused_features):.4f}")
            
        except Exception as e:
            print(f"特征提取失败: {e}")

def compare_features():
    """比较不同特征的效果"""
    
    print("\n" + "=" * 50)
    print("特征比较分析")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    extractor = ProteinFeatureExtractor()
    fusion_model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)
    fusion_model.to(device)
    
    # 测试序列
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    
    print(f"测试序列长度: {len(sequence)}")
    
    # 1. 提取原始特征
    print("\n1. 提取原始特征...")
    all_features = extractor.extract_features(sequence)
    
    esm_features = all_features['esm2']['residue_embeddings']
    msa_features = all_features['msa']
    
    print(f"   ESM-2特征形状: {esm_features.shape}")
    print(f"   MSA特征类型: {type(msa_features)}")
    
    # 2. 提取融合特征
    print("\n2. 提取融合特征...")
    fusion_results = fusion_model.extract_and_fuse(sequence, extractor)
    
    fused_features = fusion_results['fused_features']
    enhanced_esm = fusion_results['enhanced_esm_features']
    enhanced_msa = fusion_results['enhanced_msa_features']
    
    print(f"   融合特征形状: {fused_features.shape}")
    print(f"   增强ESM特征形状: {enhanced_esm.shape}")
    print(f"   增强MSA特征形状: {enhanced_msa.shape}")
    
    # 3. 特征质量分析
    print("\n3. 特征质量分析...")
    
    # 转换为numpy进行分析
    esm_np = esm_features.cpu().numpy()
    fused_np = fused_features.squeeze(0).cpu().numpy()  # 移除batch维度
    enhanced_esm_np = enhanced_esm.squeeze(0).cpu().numpy()
    
    print(f"   原始ESM-2特征:")
    print(f"     维度: {esm_np.shape[-1]}")
    print(f"     均值: {np.mean(esm_np):.4f}")
    print(f"     标准差: {np.std(esm_np):.4f}")
    
    print(f"   融合特征:")
    print(f"     维度: {fused_np.shape[-1]}")
    print(f"     均值: {np.mean(fused_np):.4f}")
    print(f"     标准差: {np.std(fused_np):.4f}")
    
    print(f"   增强ESM特征:")
    print(f"     维度: {enhanced_esm_np.shape[-1]}")
    print(f"     均值: {np.mean(enhanced_esm_np):.4f}")
    print(f"     标准差: {np.std(enhanced_esm_np):.4f}")
    
    # 4. 特征相似性分析
    print("\n4. 特征相似性分析...")
    
    # 计算余弦相似度
    def cosine_similarity(a, b):
        return np.dot(a.flatten(), b.flatten()) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    # 将ESM特征投影到相同维度进行比较
    esm_projected = esm_np[:, :512]  # 取前512维
    
    sim_esm_fused = cosine_similarity(esm_projected, fused_np)
    sim_esm_enhanced = cosine_similarity(esm_projected, enhanced_esm_np[:, :512])
    
    print(f"   ESM vs 融合特征相似度: {sim_esm_fused:.4f}")
    print(f"   ESM vs 增强ESM相似度: {sim_esm_enhanced:.4f}")

def usage_recommendations():
    """使用建议"""
    
    print("\n" + "=" * 50)
    print("使用建议")
    print("=" * 50)
    
    print("""
   直接使用场景:
    蛋白质特征提取
    序列表示学习
    蛋白质相似性分析
    聚类分析
    可视化分析

    需要训练的场景:
     特定下游任务 (如蛋白质分类、功能预测)
     领域特定优化
     任务特定的注意力模式学习

   当前特征质量:
    ESM-2特征: 高质量 (预训练)
     MSA特征: 高质量 (预训练)
     融合层: 随机初始化 (可用但未优化)

 推荐工作流程:
  1. 直接使用提取特征
  2. 在下游任务上评估效果
  3. 如需要，针对特定任务微调融合层
  4. 保持ESM-2和MSA模型冻结
    """)

def main():
    """主函数"""
    
    # 直接使用演示
    direct_feature_extraction_demo()
    
    # 特征比较
    compare_features()
    
    # 使用建议
    usage_recommendations()
    
    print("\n" + "=" * 50)
    print("演示完成!")
    print("您可以直接使用这个程序处理蛋白质序列获得融合特征!")
    print("=" * 50)

if __name__ == "__main__":
    main()
