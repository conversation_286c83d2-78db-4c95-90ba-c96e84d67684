#!/bin/bash
# HHblits数据库下载脚本

echo "🗄️ HHblits数据库下载脚本"
echo "================================"

# 创建数据库目录
DB_DIR="/usr/XML/日常工作/朱星学/hhblits_databases"
mkdir -p "$DB_DIR"
cd "$DB_DIR"

echo "📁 数据库目录: $DB_DIR"

# 检查可用空间
echo "💾 检查磁盘空间..."
df -h "$DB_DIR"

echo ""
echo "📋 可用数据库选项:"
echo "1. UniClust30 (2018_08) - 推荐，约50GB"
echo "2. PDB70 - 较小，约2GB"
echo "3. Pfam - 最小，约500MB"
echo ""

# 方案1: UniClust30数据库（推荐）
download_uniclust30() {
    echo "⬇️ 下载UniClust30数据库..."
    echo "警告: 这个数据库约50GB，下载时间较长"
    read -p "确认下载? (y/N): " confirm
    
    if [[ $confirm == [yY] ]]; then
        echo "开始下载UniClust30..."
        wget http://wwwuser.gwdg.de/~compbiol/uniclust/2018_08/uniclust30_2018_08_hhsuite.tar.gz
        
        if [ $? -eq 0 ]; then
            echo "解压数据库..."
            tar -xzf uniclust30_2018_08_hhsuite.tar.gz
            echo "✅ UniClust30下载完成"
            echo "数据库路径: $DB_DIR/uniclust30_2018_08/uniclust30_2018_08"
        else
            echo "❌ 下载失败"
        fi
    fi
}

# 方案2: PDB70数据库（较小）
download_pdb70() {
    echo "⬇️ 下载PDB70数据库..."
    echo "大小约2GB"
    
    wget http://wwwuser.gwdg.de/~compbiol/data/hhsuite/databases/hhsuite_dbs/pdb70_from_mmcif_latest.tar.gz
    
    if [ $? -eq 0 ]; then
        echo "解压数据库..."
        tar -xzf pdb70_from_mmcif_latest.tar.gz
        echo "✅ PDB70下载完成"
        echo "数据库路径: $DB_DIR/pdb70"
    else
        echo "❌ 下载失败"
    fi
}

# 方案3: Pfam数据库（最小）
download_pfam() {
    echo "⬇️ 下载Pfam数据库..."
    echo "大小约500MB"
    
    wget http://wwwuser.gwdg.de/~compbiol/data/hhsuite/databases/hhsuite_dbs/pfamA_35.0.tar.gz
    
    if [ $? -eq 0 ]; then
        echo "解压数据库..."
        tar -xzf pfamA_35.0.tar.gz
        echo "✅ Pfam下载完成"
        echo "数据库路径: $DB_DIR/pfam"
    else
        echo "❌ 下载失败"
    fi
}

# 主菜单
echo "请选择要下载的数据库:"
echo "1) UniClust30 (推荐，50GB)"
echo "2) PDB70 (2GB)"
echo "3) Pfam (500MB)"
echo "4) 全部下载"
echo "5) 退出"

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        download_uniclust30
        ;;
    2)
        download_pdb70
        ;;
    3)
        download_pfam
        ;;
    4)
        echo "下载全部数据库..."
        download_pfam
        download_pdb70
        download_uniclust30
        ;;
    5)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "📋 下载完成后的使用方法:"
echo "在代码中设置数据库路径:"
echo "  UniClust30: $DB_DIR/uniclust30_2018_08/uniclust30_2018_08"
echo "  PDB70: $DB_DIR/pdb70"
echo "  Pfam: $DB_DIR/pfam"
echo ""
echo "🔧 或者修改特征提取器中的数据库路径配置"
