# Utils.py 使用指南

## 概述

`utils.py` 提供了一套完整的工具函数，用于分析、可视化和管理attention_fusion模型的结果。

## 主要功能

### 1. 输入验证 🔍

```python
import utils

# 验证模型输入是否有效
is_valid = utils.validate_attention_fusion_input(esm_features, msa_features)
if is_valid:
    print("输入有效，可以进行模型推理")
else:
    print("输入无效，请检查数据格式")
```

**功能**: 检查ESM特征和MSA特征是否符合模型要求
**返回**: `bool` - 输入是否有效

### 2. 注意力分析摘要 📊

```python
# 运行模型获得结果
results = model(esm_features, msa_features)

# 打印注意力分析摘要
utils.print_attention_summary(results)
```

**功能**: 打印详细的注意力权重分析摘要
**输出**: 注意力层数、权重形状、平均值、贡献度等

### 3. 特征相似性计算 🔗

```python
# 计算余弦相似性
similarity = utils.compute_feature_similarity(
    features1, features2, method="cosine"
)
print(f"平均相似性: {similarity['mean_similarity']:.4f}")

# 计算L2距离
distance = utils.compute_feature_similarity(
    features1, features2, method="l2"
)
print(f"平均距离: {distance['mean_distance']:.4f}")
```

**功能**: 计算两组特征之间的相似性
**方法**: `"cosine"` (余弦相似性) 或 `"l2"` (欧几里得距离)
**返回**: 包含统计信息的字典

### 4. 数据保存和加载 💾

```python
# 保存分析结果
utils.save_attention_analysis(results, "analysis_results.npz")

# 加载分析结果
loaded_results = utils.load_attention_analysis("analysis_results.npz")
```

**功能**: 保存和加载模型分析结果
**格式**: NumPy压缩格式 (.npz)

### 5. 注意力权重可视化 🎨

```python
# 可视化单个注意力权重矩阵
utils.visualize_attention_weights(
    attention_weights=attention_matrix,
    sequence=protein_sequence,
    title="Attention Weights",
    save_path="attention_heatmap.png"
)
```

**功能**: 生成注意力权重热图
**输入**: 注意力权重矩阵、蛋白质序列
**输出**: 热图可视化文件

### 6. 交叉注意力流可视化 🌊

```python
# 可视化ESM-2和MSA之间的交叉注意力
utils.visualize_cross_attention_flow(
    esm_to_msa_weights=esm_to_msa,
    msa_to_esm_weights=msa_to_esm,
    sequence=protein_sequence,
    save_path="cross_attention.png"
)
```

**功能**: 可视化双向交叉注意力流
**输入**: ESM→MSA和MSA→ESM注意力权重
**输出**: 并排的交叉注意力热图

### 7. 特征演化可视化 📈

```python
# 可视化特征在不同层的演化
features_by_layer = [layer1_features, layer2_features, final_features]
utils.plot_feature_evolution(
    features_by_layer=features_by_layer,
    sequence=protein_sequence,
    save_path="feature_evolution.png"
)
```

**功能**: 显示特征在不同层的变化
**输入**: 不同层的特征列表
**输出**: 特征演化可视化

### 8. 注意力模式分析 🔬

```python
# 详细分析注意力模式
analysis = utils.analyze_attention_patterns(attention_weights, sequence)

print(f"注意力集中度: {analysis['esm_to_msa_concentration']:.4f}")
print(f"注意力熵: {analysis['esm_to_msa_entropy']:.4f}")
print(f"对角线注意力: {analysis['esm_to_msa_diagonal']:.4f}")
```

**功能**: 计算注意力模式的统计指标
**返回**: 包含集中度、熵、对角线权重等的字典

## 完整使用示例

```python
import torch
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel
import utils

# 1. 初始化
extractor = ProteinFeatureExtractor()
model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512)

# 2. 处理蛋白质序列
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
all_features = extractor.extract_features(sequence)
esm_features = all_features['esm2']['residue_embeddings'].unsqueeze(0)
msa_features = all_features['msa']

# 3. 验证输入
if utils.validate_attention_fusion_input(esm_features, msa_features):
    # 4. 运行模型
    results = model(esm_features, msa_features)
    
    # 5. 分析结果
    utils.print_attention_summary(results)
    
    # 6. 计算特征相似性
    similarity = utils.compute_feature_similarity(
        results['fused_features'], 
        results['enhanced_esm_features']
    )
    
    # 7. 保存结果
    utils.save_attention_analysis(results, "my_analysis.npz")
    
    # 8. 可视化
    if results['attention_weights']:
        first_layer = results['attention_weights'][0]
        utils.visualize_attention_weights(
            first_layer['esm_to_msa_weights'].squeeze(0),
            sequence,
            save_path="my_attention.png"
        )
```

## 输出文件说明

### 分析数据文件
- **格式**: `.npz` (NumPy压缩格式)
- **内容**: 所有模型输出的张量数据
- **用途**: 后续分析、比较、重现结果

### 可视化图像文件
- **注意力热图**: 显示位置间的注意力权重
- **交叉注意力流**: 显示ESM-2和MSA间的信息流
- **特征演化图**: 显示特征在不同层的变化

## 使用建议

### 1. 工作流程
1. 先运行模型获得结果
2. 使用 `print_attention_summary()` 快速了解整体情况
3. 使用可视化函数深入分析感兴趣的模式
4. 保存重要的分析结果供后续使用

### 2. 性能优化
- 对于长序列，可视化可能较慢，考虑截取关键区域
- 大批量分析时，使用保存/加载功能避免重复计算

### 3. 分析重点
- 注意力集中度：高值表示注意力集中在少数位置
- 注意力熵：低值表示注意力分布不均匀
- 对角线权重：高值表示位置倾向于关注自身

### 4. 故障排除
- 确保输入张量在正确的设备上 (CPU/GPU)
- 检查张量形状是否符合要求
- 对于可视化问题，确保matplotlib后端设置正确

## 扩展功能

utils.py 设计为可扩展的，您可以：
- 添加新的相似性度量方法
- 实现自定义的可视化样式
- 扩展注意力模式分析指标
- 添加新的数据导出格式

通过这些工具函数，您可以深入理解attention_fusion模型的行为，优化模型性能，并生成高质量的分析报告。
