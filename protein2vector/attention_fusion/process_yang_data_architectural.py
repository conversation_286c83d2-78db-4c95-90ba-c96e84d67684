#!/usr/bin/env python3
"""
处理Yang_Data.xlsx文件，使用attention fusion方法提取蛋白质序列的architectural融合特征
基于process_km_data_batch.py修改
"""

import pandas as pd
import numpy as np
import torch
import os
import sys
from pathlib import Path
import logging
from tqdm import tqdm
import json
from datetime import datetime

# 导入attention_fusion模块
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YangDataProcessor:
    def __init__(self):
        self.input_file = "Yang_Data.xlsx"
        self.output_file = "architectural_fusion.csv"
        self.device = None
        self.extractor = None
        self.model = None
        
    def setup_models(self):
        """初始化模型"""
        logger.info("初始化attention_fusion模型...")
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        
        # 初始化特征提取器
        self.extractor = ProteinFeatureExtractor(
            esm_model_name="facebook/esm2_t33_650M_UR50D",
            msa_model_name="facebook/esm_msa1b_t12_100M_UR50S"
        )
        
        # 初始化融合模型
        self.model = AttentionFusionModel(
            esm_dim=1280, 
            msa_dim=768,
            hidden_dim=512, 
            num_blocks=2, 
            num_heads=4
        )
        self.model.to(self.device)
        self.model.eval()
        
        logger.info("[SUCCESS] 模型初始化完成")
    
    def extract_architectural_fusion_features(self, sequence):
        """提取单个序列的architectural融合特征"""
        try:
            # 检查序列长度 - 使用ESM-2最大允许长度1024
            if len(sequence) > 1024:
                logger.warning(f"序列长度 {len(sequence)} 超过1024，截取前1024个氨基酸")
                sequence = sequence[:1024]
            
            # 提取融合特征
            results = self.model.extract_and_fuse(sequence, self.extractor)
            
            # 获取融合特征 (batch_size, seq_len, hidden_dim)
            fused_features = results["fused_features"]  # [1, seq_len, 512]
            
            # 计算序列级表示 (平均池化) - architectural特征
            sequence_representation = fused_features.mean(dim=1).squeeze(0)  # [512]
            
            # 转换为numpy数组
            feature_vector = sequence_representation.detach().cpu().numpy()
            
            return feature_vector, True, None
            
        except Exception as e:
            logger.error(f"处理序列时出错: {str(e)}")
            return None, False, str(e)
    
    def process_yang_data(self):
        """处理Yang_Data.xlsx文件"""
        logger.info("开始处理Yang_Data.xlsx文件")
        
        # 检查输入文件是否存在
        if not os.path.exists(self.input_file):
            logger.error(f"输入文件不存在: {self.input_file}")
            return
        
        # 读取数据
        logger.info("读取Excel文件...")
        df = pd.read_excel(self.input_file)
        logger.info(f"数据形状: {df.shape}")
        logger.info(f"列名: {list(df.columns)}")
        
        # 检查必需的列
        required_cols = ['id', 'name', 'Sequence']
        for col in required_cols:
            if col not in df.columns:
                logger.error(f"数据文件中缺少列: {col}")
                return
        
        # 提取需要的列
        work_df = df[['id', 'name', 'Sequence']].copy()
        total_sequences = len(work_df)
        logger.info(f"总共需要处理 {total_sequences} 条序列")
        
        # 序列长度统计
        seq_lengths = work_df['Sequence'].str.len()
        logger.info(f"序列长度统计: 最短={seq_lengths.min()}, 最长={seq_lengths.max()}, 平均={seq_lengths.mean():.1f}")
        
        # 初始化模型
        self.setup_models()
        
        # 处理每个序列
        results = []
        logger.info("开始提取architectural融合特征...")
        
        for idx, row in tqdm(work_df.iterrows(), total=len(work_df), desc="处理序列"):
            seq_id = row['id']
            seq_name = row['name']
            sequence = row['Sequence']
            
            # 检查序列是否有效
            if pd.isna(sequence) or len(sequence) < 8:
                logger.warning(f"序列 {seq_id} 无效，跳过")
                results.append({
                    'id': seq_id,
                    'name': seq_name,
                    'Sequence': sequence,
                    'vector': None,
                    'success': False,
                    'error': 'Invalid sequence'
                })
                continue
            
            # 提取特征
            logger.info(f"处理序列 {seq_id}: {seq_name}")
            feature_vector, success, error = self.extract_architectural_fusion_features(sequence)
            
            if success and feature_vector is not None:
                # 将特征向量转换为逗号分隔的字符串
                vector_str = ','.join(map(str, feature_vector))
                results.append({
                    'id': seq_id,
                    'name': seq_name,
                    'Sequence': sequence,
                    'vector': vector_str,
                    'success': True,
                    'error': None
                })
                logger.info(f"[SUCCESS] 序列 {seq_id} 处理成功，特征维度: {len(feature_vector)}")
            else:
                results.append({
                    'id': seq_id,
                    'name': seq_name,
                    'Sequence': sequence,
                    'vector': None,
                    'success': False,
                    'error': error
                })
                logger.error(f"[ERROR] 序列 {seq_id} 处理失败: {error}")
        
        # 创建输出数据框
        output_df = pd.DataFrame(results)
        
        # 保存结果
        logger.info(f"保存结果到 {self.output_file}")
        output_df.to_csv(self.output_file, index=False)
        
        # 统计信息
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        success_rate = successful / len(results) * 100 if results else 0
        
        logger.info("=" * 50)
        logger.info("处理完成!")
        logger.info(f"总序列数: {len(results)}")
        logger.info(f"成功处理: {successful}")
        logger.info(f"处理失败: {failed}")
        logger.info(f"成功率: {success_rate:.1f}%")
        
        if successful > 0:
            # 获取特征维度
            first_success = next(r for r in results if r['success'])
            if first_success['vector']:
                feature_dim = len(first_success['vector'].split(','))
                logger.info(f"特征维度: {feature_dim}")
        
        logger.info(f"结果已保存到: {self.output_file}")

def main():
    """主函数"""
    # 创建处理器
    processor = YangDataProcessor()
    
    # 处理数据
    processor.process_yang_data()

if __name__ == "__main__":
    main()
