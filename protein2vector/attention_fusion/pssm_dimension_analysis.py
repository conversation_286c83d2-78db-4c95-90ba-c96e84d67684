#!/usr/bin/env python3
"""
PSSM矩阵维度结构详细分析
解释300氨基酸蛋白质的PSSM矩阵维度构成
"""

import numpy as np
import pandas as pd

def explain_pssm_dimensions():
    """详细解释PSSM矩阵的维度结构"""
    
    print("=" * 80)
    print("PSSM矩阵维度结构详细分析")
    print("=" * 80)
    
    # 示例参数
    protein_length = 300  # 蛋白质长度
    num_amino_acids = 20  # 标准氨基酸数量
    
    print(f"🧬 示例蛋白质:")
    print(f"   长度: {protein_length} 个氨基酸残基")
    print(f"   标准氨基酸: {num_amino_acids} 种")
    
    print(f"\n📊 PSSM矩阵维度:")
    print(f"   形状: ({protein_length}, {num_amino_acids})")
    print(f"   总元素数: {protein_length * num_amino_acids:,}")
    
    print(f"\n🔍 维度含义:")
    print(f"   第一维 (行): {protein_length} → 每个氨基酸位置")
    print(f"   第二维 (列): {num_amino_acids} → 20种氨基酸的概率")
    
    return protein_length, num_amino_acids

def demonstrate_pssm_structure():
    """演示PSSM矩阵的具体结构"""
    
    print("\n" + "=" * 80)
    print("PSSM矩阵结构演示")
    print("=" * 80)
    
    # 氨基酸列表
    amino_acids = list("ACDEFGHIKLMNPQRSTVWY")
    print(f"20种标准氨基酸: {' '.join(amino_acids)}")
    
    # 示例：300氨基酸蛋白质的前5个位置
    protein_length = 300
    demo_positions = 5
    
    print(f"\n📋 PSSM矩阵结构 (显示前{demo_positions}个位置):")
    print(f"{'位置':<6} " + " ".join([f"{aa:>6}" for aa in amino_acids]))
    print("-" * (6 + 7 * 20))
    
    # 模拟PSSM数据
    np.random.seed(42)  # 固定随机种子
    
    for pos in range(demo_positions):
        # 生成随机概率分布（模拟真实PSSM）
        probs = np.random.dirichlet(np.ones(20) * 0.5)  # 生成概率分布
        
        print(f"{pos+1:>4}   " + " ".join([f"{prob:>6.3f}" for prob in probs]))
    
    print(f"{'...':>6} " + " ".join([f"{'...':>6}" for _ in amino_acids]))
    print(f"{protein_length:>4}   " + " ".join([f"{'X.XXX':>6}" for _ in amino_acids]))
    
    print(f"\n💡 解释:")
    print(f"   - 每一行代表一个氨基酸位置的概率分布")
    print(f"   - 每一列代表特定氨基酸在所有位置的概率")
    print(f"   - 每行的20个数值之和 = 1.0 (概率分布)")

def clarify_common_misconception():
    """澄清常见误解"""
    
    print("\n" + "=" * 80)
    print("❌ 常见误解 vs ✅ 正确理解")
    print("=" * 80)
    
    print("❌ 错误理解:")
    print("   '300氨基酸蛋白质的20维PSSM是300个位置的平均值'")
    print("   → 这会丢失位置特异性信息！")
    
    print("\n✅ 正确理解:")
    print("   '300氨基酸蛋白质的PSSM是 (300, 20) 矩阵'")
    print("   → 保留每个位置的完整信息！")
    
    print(f"\n📐 维度对比:")
    
    comparison_data = [
        ["错误理解", "平均PSSM", "(20,)", "20", "丢失位置信息"],
        ["正确理解", "完整PSSM", "(300, 20)", "6,000", "保留位置信息"],
    ]
    
    df = pd.DataFrame(comparison_data, columns=["理解方式", "特征类型", "维度", "元素数", "信息保留"])
    print(df.to_string(index=False))

def explain_integration_process():
    """解释PSSM如何整合到768维特征中"""
    
    print("\n" + "=" * 80)
    print("PSSM整合到768维特征的过程")
    print("=" * 80)
    
    protein_length = 300
    
    print(f"🔄 整合过程:")
    print(f"   1. 计算PSSM矩阵: ({protein_length}, 20)")
    print(f"   2. 计算保守性分数: ({protein_length}, 1)")
    print(f"   3. 随机填充: ({protein_length}, 747)")
    print(f"   4. 水平拼接: ({protein_length}, 768)")
    
    print(f"\n📊 最终特征矩阵:")
    print(f"   形状: ({protein_length}, 768)")
    print(f"   含义: 每个氨基酸位置都有768维特征向量")
    
    print(f"\n🎯 关键点:")
    print(f"   - PSSM不是被平均的！")
    print(f"   - 每个位置保持独立的20维PSSM向量")
    print(f"   - 位置特异性信息完全保留")
    print(f"   - 768维是特征维度，不是序列长度")

def demonstrate_real_example():
    """演示真实的PSSM计算示例"""
    
    print("\n" + "=" * 80)
    print("真实PSSM计算示例")
    print("=" * 80)
    
    # 模拟MSA序列
    msa_sequences = [
        "MKLLVL",  # 查询序列
        "MKLLVL",  # 相同序列
        "MKLLVL",  # 相同序列  
        "MKLLAL",  # 位置5: V->A
        "MKLLIL",  # 位置5: V->I
        "MKLLTL",  # 位置5: V->T
    ]
    
    print(f"📝 示例MSA (6条序列):")
    for i, seq in enumerate(msa_sequences):
        print(f"   序列{i+1}: {seq}")
    
    # 计算PSSM
    aa_to_idx = {aa: i for i, aa in enumerate("ACDEFGHIKLMNPQRSTVWY")}
    seq_len = len(msa_sequences[0])
    pssm = np.zeros((seq_len, 20))
    
    print(f"\n🧮 PSSM计算过程:")
    
    for pos in range(seq_len):
        aa_counts = np.zeros(20)
        total_count = 0
        
        print(f"\n位置 {pos+1} ({msa_sequences[0][pos]}):")
        
        # 统计每个氨基酸的出现次数
        position_counts = {}
        for seq in msa_sequences:
            if pos < len(seq) and seq[pos] in aa_to_idx:
                aa = seq[pos]
                position_counts[aa] = position_counts.get(aa, 0) + 1
                aa_counts[aa_to_idx[aa]] += 1
                total_count += 1
        
        # 显示统计结果
        for aa, count in position_counts.items():
            freq = count / total_count
            print(f"   {aa}: {count}/{total_count} = {freq:.3f}")
        
        # 计算概率分布
        if total_count > 0:
            pssm[pos] = aa_counts / total_count
        else:
            pssm[pos] = np.ones(20) / 20
        
        # 显示PSSM向量（只显示非零元素）
        non_zero_indices = np.where(pssm[pos] > 0)[0]
        non_zero_aas = [list("ACDEFGHIKLMNPQRSTVWY")[i] for i in non_zero_indices]
        non_zero_probs = pssm[pos][non_zero_indices]
        
        print(f"   PSSM向量: {dict(zip(non_zero_aas, non_zero_probs))}")
    
    print(f"\n📊 最终PSSM矩阵:")
    print(f"   形状: {pssm.shape}")
    print(f"   每行和: {[f'{pssm[i].sum():.3f}' for i in range(seq_len)]}")
    print(f"   验证: 每行和都应该等于1.0 ✅")

def main():
    """主函数"""
    
    # 解释PSSM维度
    explain_pssm_dimensions()
    
    # 演示PSSM结构
    demonstrate_pssm_structure()
    
    # 澄清误解
    clarify_common_misconception()
    
    # 解释整合过程
    explain_integration_process()
    
    # 真实示例
    demonstrate_real_example()
    
    print("\n" + "=" * 80)
    print("📋 总结")
    print("=" * 80)
    print("对于300氨基酸的蛋白质:")
    print("✅ PSSM矩阵维度: (300, 20)")
    print("✅ 每个位置保留独立的20维概率分布")
    print("✅ 不是300个位置的平均值！")
    print("✅ 位置特异性信息完全保留")
    print("✅ 最终整合到768维特征: (300, 768)")
    print("\n这样才能保持蛋白质序列的位置特异性信息！")

if __name__ == "__main__":
    main()
